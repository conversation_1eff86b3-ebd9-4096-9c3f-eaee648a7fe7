#include "esp_log.h"
#include "stdlib.h"
#include "string.h"
#include "stdbool.h"
// 头文件保留mp3解码相关
#include "mp3_decoder.h"
#include "netHandle.h"
#include "audio.h"

static const char *TAG = "audio";

// 修改全局变量区域
static i2s_chan_handle_t i2s_tx_handle = NULL;


unsigned int g_playTaskId=0;
unsigned int g_playTaskSrc=0;
unsigned int g_playIndex = 0;
unsigned int g_need_download_pos = 0;
unsigned int g_has_download_pos = 0;

unsigned int g_song_total_frames=0;
unsigned int g_audio_channel_num = 2;   //默认双声道，即使是单声道，也要转换成双声道再送给I2S
unsigned int g_audio_sample_rate = 44100;
unsigned int g_song_length = 0; //歌曲的大小（字节）

bool can_get_song_stream_flag = false;
bool g_stream_ready_play = false;

stStreamBuf g_streamBuf;

static audio_element_handle_t g_mp3_decoder = NULL;

ringbuf_handle_t audio_data_rb=NULL;

// 在全局变量区域添加（已有其他全局变量）
static SemaphoreHandle_t ringbuf_mutex = NULL;


unsigned int soundCard_stream_cnt=0; //声卡采集音频流计数



void cleanPlayTask(bool isStopTask)
{
  if(g_playTaskSrc !=TASK_SRC_NONE)
  {
    g_stream_ready_play = false;

    //加入标志，下一曲时isStopTask=false,不重置g_playTaskId和task_details
    if(isStopTask)
    {
        g_playTaskId = 0;
        g_playTaskSrc = TASK_SRC_NONE;
    }

    g_need_download_pos = 0;
    g_has_download_pos = 0;

    vTaskDelay(110/portTICK_PERIOD_MS);
  }
}


esp_err_t ringbuf_init(int block_size, int n_blocks)
{
    if(audio_data_rb) {
        return ESP_OK;
    }
    
    // 只需创建一次互斥锁
    if (!ringbuf_mutex) {
        ringbuf_mutex = xSemaphoreCreateMutex();
        if (!ringbuf_mutex) {
            ESP_LOGE(TAG, "Failed to create ringbuf mutex");
            return ESP_FAIL;
        }
    }
    
    xSemaphoreTake(ringbuf_mutex, portMAX_DELAY);
    // 创建环形缓冲区
    audio_data_rb = rb_create(block_size, n_blocks);
    xSemaphoreGive(ringbuf_mutex);
    
    if (!audio_data_rb) {
        ESP_LOGE(TAG, "Failed to create ring buffer");
        return ESP_FAIL;
    }

    return ESP_OK;
}

esp_err_t ringbuf_deinit()
{
    if (!audio_data_rb) {
        return ESP_FAIL;
    }

    if (ringbuf_mutex) {
        xSemaphoreTake(ringbuf_mutex, portMAX_DELAY);
        // 仅销毁环形缓冲区，保留互斥锁
        if (audio_data_rb) {
            rb_destroy(audio_data_rb);
            audio_data_rb = NULL;
            ESP_LOGI(TAG, "ringbuf_deinit: audio_data_rb destroy success!");
        }
        xSemaphoreGive(ringbuf_mutex);
    }

    return ESP_OK;
}


esp_err_t ringbuf_write(char *buf, int buf_len, TickType_t ticks_to_wait)
{
    if (!audio_data_rb) {
        return ESP_FAIL;
    }

    if (ringbuf_mutex) {
        xSemaphoreTake(ringbuf_mutex, portMAX_DELAY);
        // 仅销毁环形缓冲区，保留互斥锁
        if (audio_data_rb) {
            rb_write(audio_data_rb, (char *)buf, buf_len, ticks_to_wait);   //ticks_to_wait为0代表不阻塞
        }
        else
        {
            xSemaphoreGive(ringbuf_mutex);
            return ESP_FAIL;
        }
        xSemaphoreGive(ringbuf_mutex);
    }

    return ESP_OK;

}