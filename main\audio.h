#ifndef AUDIO_H
#define AUDIO_H

#include "driver/i2s_common.h"
#include "driver/i2s_std.h"
#include "stdbool.h"


#include "ringbuf.h"
#include "freertos/semphr.h"
#include "audio_pipeline.h"

// 新增I2S配置常量及函数声明
#define I2S_PORT_NUM      I2S_NUM_0
#define I2S_MCLK_IO       GPIO_NUM_10
#define I2S_SCLK_IO       GPIO_NUM_7
#define I2S_LRCK_IO       GPIO_NUM_6
#define I2S_DOUT_IO       GPIO_NUM_8
#define I2S_DIN_IO        I2S_GPIO_UNUSED


#define AUDIO_STREAM_DOWNLOAD_NUM_ONCE  20

#define RINGBUF_MP3_BLOCK_SIZE  240
#define RINGBUF_MP3_BLOCKS_NUM  120

#define RINGBUF_G722_BLOCK_SIZE  256
#define RINGBUF_G722_BLOCKS_NUM  30

#define RINGBUF_OPUS_BLOCK_SIZE  320
#define RINGBUF_OPUS_BLOCKS_NUM  80

typedef struct{
    unsigned char *stream;
    unsigned short *len;
    unsigned short streamSize;
    unsigned short streamNum;
    bool *bValid;
    bool bInit;
    unsigned int read_pos;
    unsigned int write_pos;
    unsigned int read_pos_total;
    unsigned int write_pos_total;
}stStreamBuf;

extern unsigned int g_playTaskId;
extern unsigned int g_playTaskSrc;
extern unsigned int g_playIndex;
extern unsigned int g_need_download_pos;
extern unsigned int g_has_download_pos;

extern unsigned int g_song_total_frames;
extern unsigned int g_audio_channel_num;
extern unsigned int g_audio_sample_rate;
extern unsigned int g_song_length;

extern bool can_get_song_stream_flag;
extern bool g_stream_ready_play;

extern stStreamBuf g_streamBuf;

extern ringbuf_handle_t audio_data_rb;

extern unsigned int soundCard_stream_cnt; //声卡采集音频流计数

// 新增初始化函数声明
esp_err_t ringbuf_init(int block_size, int n_blocks);
esp_err_t ringbuf_deinit();
esp_err_t ringbuf_write(char *buf, int buf_len, TickType_t ticks_to_wait);

void cleanPlayTask(bool isStopTask);


extern ringbuf_handle_t audio_data_rb;
extern audio_pipeline_handle_t pipeline;


void start_mp3Player_stream_task();
void start_g722Player_stream_task();
void start_opus_stream_task();
#endif