#ifndef NETHANDLE_H
#define NETHANDLE_H

void mqtt_msg_handle(char *msg, int msgLen);
unsigned char tcp_msg_handle(unsigned char messageType, unsigned char *data, int dataLen, int jsonLen);
unsigned char soundCard_tcp_handle(unsigned short command, unsigned char *data, int dataLen);

typedef enum {
    TcpHandleResult_Success = 0,
    TcpHandleResult_Failed = 1,
    TcpHandleResult_Invalid = 2,
    TcpHandleResult_NotSupport = 3,
    TcpHandleResult_TcpError = 0xFF
}TcpHandleResult;

enum {
    TASK_SRC_NONE = 0,
    TASK_SRC_SERVER_MUSIC = 1,
    TASK_SRC_CONTROLER_MUSIC = 2,
    TASK_SRC_PAGING = 3,
    TASK_SRC_WEATHER = 4,
    TASK_SRC_ALARM = 5,
    TASK_SRC_SOUNDCARD = 6
};   

void send_play_progress(unsigned int frames);
void sendRequestDataCmd(int batch,int pos);

// 缓存相关函数声明
int song_cache_read_audio_frame(uint8_t *buffer, uint32_t buffer_size, uint32_t *frame_size);
bool is_playing_from_cache(void);
void set_cache_enabled(bool enabled);
void get_cache_status(bool *enabled, uint64_t *used_space, uint64_t *free_space, uint32_t *file_count);
void cleanup_cache_on_task_end(void);

#endif