#!/usr/bin/env python3
"""
WiFi Speaker SoftAP API 测试脚本
使用方法：
1. 连接到设备的WiFi热点 (WifiSpeaker-XXXXXX)
2. 运行此脚本测试各个API接口
"""

import requests
import json
import time

# 设备IP地址
DEVICE_IP = "************"
BASE_URL = f"http://{DEVICE_IP}"

def test_device_info():
    """测试设备信息API"""
    print("=== 测试设备信息API ===")
    try:
        response = requests.get(f"{BASE_URL}/api/device/info", timeout=5)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return True
    except Exception as e:
        print(f"错误: {e}")
        return False

def test_wifi_scan():
    """测试WiFi扫描API"""
    print("\n=== 测试WiFi扫描API ===")
    try:
        response = requests.get(f"{BASE_URL}/api/wifi/scan", timeout=10)
        print(f"状态码: {response.status_code}")
        data = response.json()
        print(f"扫描到 {len(data.get('data', []))} 个WiFi网络")
        for wifi in data.get('data', [])[:5]:  # 只显示前5个
            print(f"  - {wifi['ssid']} (信号强度: {wifi['rssi']}dBm, 加密: {wifi.get('authType', 'Unknown')})")
        return True
    except Exception as e:
        print(f"错误: {e}")
        return False

def test_wifi_status():
    """测试WiFi状态API"""
    print("\n=== 测试WiFi状态API ===")
    try:
        response = requests.get(f"{BASE_URL}/api/wifi/status", timeout=5)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return True
    except Exception as e:
        print(f"错误: {e}")
        return False

def test_wifi_config(ssid, password):
    """测试WiFi配置API"""
    print(f"\n=== 测试WiFi配置API (SSID: {ssid}) ===")
    try:
        data = {
            "ssid": ssid,
            "password": password
        }
        response = requests.post(
            f"{BASE_URL}/api/wifi/config", 
            json=data, 
            headers={'Content-Type': 'application/json'},
            timeout=5
        )
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return True
    except Exception as e:
        print(f"错误: {e}")
        return False

def test_wechat_device_info():
    """测试微信小程序设备信息API"""
    print("\n=== 测试微信小程序设备信息API ===")
    try:
        response = requests.get(f"{BASE_URL}/wechat/device/info", timeout=5)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return True
    except Exception as e:
        print(f"错误: {e}")
        return False

def test_wechat_device_control():
    """测试微信小程序设备控制API"""
    print("\n=== 测试微信小程序设备控制API ===")
    try:
        # 测试获取音量
        data = {"cmd": 2}  # DEVICE_CMD_VOLUME_GET
        response = requests.post(
            f"{BASE_URL}/wechat/device/control", 
            json=data, 
            headers={'Content-Type': 'application/json'},
            timeout=5
        )
        print(f"获取音量 - 状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        
        # 测试设置音量
        data = {"cmd": 1, "value": 75}  # DEVICE_CMD_VOLUME_SET
        response = requests.post(
            f"{BASE_URL}/wechat/device/control", 
            json=data, 
            headers={'Content-Type': 'application/json'},
            timeout=5
        )
        print(f"设置音量 - 状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return True
    except Exception as e:
        print(f"错误: {e}")
        return False

def test_wechat_system_info():
    """测试微信小程序系统信息API"""
    print("\n=== 测试微信小程序系统信息API ===")
    try:
        response = requests.get(f"{BASE_URL}/wechat/system/info", timeout=5)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return True
    except Exception as e:
        print(f"错误: {e}")
        return False

def main():
    """主测试函数"""
    print("WiFi Speaker SoftAP API 测试")
    print("=" * 50)
    
    # 基础连接测试
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"设备连接正常 (状态码: {response.status_code})")
    except Exception as e:
        print(f"无法连接到设备: {e}")
        print("请确认：")
        print("1. 已连接到设备的WiFi热点")
        print("2. 设备IP地址正确 (************)")
        return
    
    # 运行所有测试
    tests = [
        test_device_info,
        test_wifi_scan,
        test_wifi_status,
        test_wechat_device_info,
        test_wechat_device_control,
        test_wechat_system_info
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
        time.sleep(1)  # 避免请求过快
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{len(tests)} 个测试")
    
    # 可选的WiFi配置测试
    print("\n注意: WiFi配置测试需要真实的WiFi信息")
    print("如需测试WiFi配置，请手动调用:")
    print("test_wifi_config('your_wifi_ssid', 'your_wifi_password')")

if __name__ == "__main__":
    main()
