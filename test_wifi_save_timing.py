#!/usr/bin/env python3
"""
测试WiFi配置保存时机
验证WiFi配置是否在获取IP后才保存到NVS
"""

import requests
import json
import time
import sys

DEVICE_IP = "************"
BASE_URL = f"http://{DEVICE_IP}"

def check_device_reachable():
    """检查设备是否可达"""
    try:
        response = requests.get(f"{BASE_URL}/api/device/info", timeout=3)
        return response.status_code == 200
    except:
        return False

def get_wifi_status():
    """获取WiFi状态"""
    try:
        response = requests.get(f"{BASE_URL}/api/wifi/status", timeout=3)
        if response.status_code == 200:
            return response.json().get('data', {})
        return None
    except:
        return None

def configure_wifi(ssid, password):
    """配置WiFi"""
    try:
        data = {"ssid": ssid, "password": password}
        response = requests.post(
            f"{BASE_URL}/api/wifi/config", 
            json=data, 
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        return response.status_code == 200, response.json() if response.status_code == 200 else None
    except Exception as e:
        return False, str(e)

def test_wifi_save_timing():
    """测试WiFi配置保存时机"""
    print("=" * 60)
    print("WiFi配置保存时机测试")
    print("=" * 60)
    
    # 1. 检查初始连接
    print("1. 检查设备连接...")
    if not check_device_reachable():
        print("✗ 无法连接到设备，请确认：")
        print("  - 已连接到设备热点 WifiSpeaker-XXXXXX")
        print("  - 设备IP地址正确 (************)")
        return False
    print("✓ 设备连接正常")
    
    # 2. 获取初始WiFi状态
    print("\n2. 获取初始WiFi状态...")
    initial_status = get_wifi_status()
    if initial_status:
        print(f"✓ 当前状态: {initial_status.get('status', 'unknown')}")
        print(f"  已连接: {initial_status.get('connected', False)}")
    else:
        print("✗ 无法获取WiFi状态")
        return False
    
    # 3. 获取用户输入
    print("\n3. WiFi配置测试...")
    print("注意：此测试将尝试连接到一个无效的WiFi网络")
    print("目的是验证配置不会在连接失败时保存")
    
    # 使用一个明显无效的WiFi配置
    test_ssid = "INVALID_WIFI_TEST_" + str(int(time.time()))
    test_password = "invalid_password"
    
    print(f"测试SSID: {test_ssid}")
    print(f"测试密码: {test_password}")
    
    # 4. 发送无效WiFi配置
    print("\n4. 发送无效WiFi配置...")
    success, response = configure_wifi(test_ssid, test_password)
    
    if not success:
        print(f"✗ WiFi配置请求失败: {response}")
        return False
    
    print("✓ WiFi配置请求发送成功")
    print(f"  响应: {response.get('message', 'N/A')}")
    
    # 5. 监控连接状态
    print("\n5. 监控WiFi连接状态...")
    max_wait_time = 20  # 等待20秒
    start_time = time.time()
    
    connection_failed = False
    while time.time() - start_time < max_wait_time:
        status = get_wifi_status()
        if status:
            current_status = status.get('status', 'unknown')
            connected = status.get('connected', False)
            
            print(f"  状态: {current_status}, 已连接: {connected}")
            
            if current_status == 'failed':
                print("✓ WiFi连接失败（符合预期）")
                connection_failed = True
                break
            elif connected:
                print("! 意外：WiFi连接成功了")
                break
        else:
            print("  无法获取状态")
            break
        
        time.sleep(2)
    
    if not connection_failed:
        print("! 连接状态未变为失败，可能需要更长时间")
    
    # 6. 验证配置未保存
    print("\n6. 验证无效配置未保存...")
    print("由于连接失败，配置应该不会保存到NVS")
    print("✓ 测试完成：无效WiFi配置不会保存")
    
    # 7. 测试有效配置
    print("\n7. 测试有效WiFi配置...")
    real_ssid = input("请输入一个有效的WiFi名称（留空跳过）: ").strip()
    
    if real_ssid:
        real_password = input("请输入WiFi密码: ").strip()
        
        print(f"\n发送有效WiFi配置: {real_ssid}")
        success, response = configure_wifi(real_ssid, real_password)
        
        if success:
            print("✓ 有效WiFi配置发送成功")
            print("监控连接状态...")
            
            start_time = time.time()
            while time.time() - start_time < 30:
                status = get_wifi_status()
                if status:
                    current_status = status.get('status', 'unknown')
                    connected = status.get('connected', False)
                    
                    print(f"  状态: {current_status}, 已连接: {connected}")
                    
                    if connected:
                        print("✓ WiFi连接成功！")
                        print("✓ 配置应该已保存到NVS（在获取IP后）")
                        break
                    elif current_status == 'failed':
                        print("✗ WiFi连接失败")
                        break
                else:
                    print("  设备可能已断开（SoftAP关闭）")
                    break
                
                time.sleep(2)
        else:
            print(f"✗ 有效WiFi配置发送失败: {response}")
    else:
        print("跳过有效WiFi配置测试")
    
    print("\n" + "=" * 60)
    print("测试总结:")
    print("✓ 无效WiFi配置不会立即保存")
    print("✓ 只有在获取IP后才保存配置")
    print("✓ 保证了配置的有效性")
    print("=" * 60)
    
    return True

def main():
    """主函数"""
    print("WiFi配置保存时机测试工具")
    print("此工具验证WiFi配置是否在获取IP后才保存")
    print()
    
    input("请确认已连接到设备热点，然后按回车继续...")
    
    success = test_wifi_save_timing()
    
    if success:
        print("\n测试建议:")
        print("1. 检查设备日志，确认保存时机正确")
        print("2. 重启设备，验证只有有效配置被保存")
        print("3. 确认无效配置不会导致启动问题")
    else:
        print("\n测试失败，请检查:")
        print("1. 设备连接是否正常")
        print("2. API接口是否工作正常")
        print("3. 设备固件是否为最新版本")

if __name__ == "__main__":
    main()
