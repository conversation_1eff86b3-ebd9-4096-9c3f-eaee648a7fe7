#include "song_cache.h"
#include "esp_log.h"
#include "esp_err.h"
#include "esp_vfs_fat.h"
#include "driver/sdmmc_host.h"
#include "driver/sdspi_host.h"
#include "sdmmc_cmd.h"
#include "ff.h"
#include "sys/stat.h"
#ifndef S_IRWXU
#define S_IRWXU 0700
#endif
#ifndef S_IRWXG
#define S_IRWXG 0070
#endif
#ifndef S_IRWXO
#define S_IRWXO 0007
#endif
#include "dirent.h"
#include "string.h"
#include "stdlib.h"
#include "time.h"
#include "errno.h"
#include "const.h"

static const char *TAG = "song_cache";

// 全局缓存管理器
static cache_manager_t g_cache_manager = {0};
static sdmmc_card_t *g_card = NULL;

// 魔数定义
#define CACHE_FILE_MAGIC 0x43414348  // "CACH"
#define CACHE_FILE_VERSION 1


void set_cache_enabled(bool enabled);

static bool _is_sdcard_readonly(void);

/**
 * 检查并修复文件系统权限
 */
static esp_err_t _check_filesystem_permissions(void)
{
    // 检查挂载点权限
    struct stat mount_stat;
    if (stat(CACHE_MOUNT_POINT, &mount_stat) != 0) {
        ESP_LOGE(TAG, "Cannot access mount point: %s", strerror(errno));
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "Mount point permissions: 0%o", mount_stat.st_mode & 0777);

    // 检查SD卡是否为只读模式
    if (_is_sdcard_readonly()) {
        ESP_LOGE(TAG, "SD card is in read-only mode");
        return ESP_FAIL;
    }

    // 尝试在缓存目录创建测试文件
    char test_path[256];
    snprintf(test_path, sizeof(test_path), "%s/test_permissions.tmp", CACHE_DIR_PATH);

    ESP_LOGI(TAG, "Attempting to create test file: %s", test_path);

    FILE *test_file = fopen(test_path, "w");
    if (!test_file) {
        ESP_LOGE(TAG, "Cannot create test file in mount point: %s (errno: %d)",
                 strerror(errno), errno);

        // 尝试使用不同的文件名和路径
        char alt_test_path[256];
        snprintf(alt_test_path, sizeof(alt_test_path), "%s/test.txt", CACHE_MOUNT_POINT);
        ESP_LOGI(TAG, "Trying alternative path: %s", alt_test_path);

        test_file = fopen(alt_test_path, "w");
        if (!test_file) {
            ESP_LOGE(TAG, "Alternative path also failed: %s (errno: %d)",
                     strerror(errno), errno);
            return ESP_FAIL;
        }

        // 更新路径变量
        strcpy(test_path, alt_test_path);
    }

    fprintf(test_file, "permission test");
    fclose(test_file);

    // 验证文件创建成功
    struct stat test_stat;
    if (stat(test_path, &test_stat) != 0) {
        ESP_LOGE(TAG, "Test file was not created successfully: %s", strerror(errno));
        return ESP_FAIL;
    }

    // 清理测试文件
    if (remove(test_path) != 0) {
        ESP_LOGW(TAG, "Failed to remove test file: %s", strerror(errno));
    }

    ESP_LOGI(TAG, "Filesystem permissions check passed");
    return ESP_OK;
}

/**
 * SD卡缓存系统初始化
 */
esp_err_t song_cache_init(void)
{
    if (g_cache_manager.initialized) {
        ESP_LOGW(TAG, "Cache already initialized");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "Initializing SD card cache system...");

    // 配置SD卡主机
    esp_vfs_fat_sdmmc_mount_config_t mount_config = {
        .format_if_mount_failed = true,  // 允许格式化失败的卡
        .max_files = 10,
        .allocation_unit_size = 16 * 1024
    };

    // 配置SDIO接口
    sdmmc_host_t host = SDMMC_HOST_DEFAULT();
    host.flags = SDMMC_HOST_FLAG_4BIT;
    host.max_freq_khz = SDMMC_FREQ_DEFAULT;

    // 配置引脚
    sdmmc_slot_config_t slot_config = SDMMC_SLOT_CONFIG_DEFAULT();
    slot_config.width = 4;
    slot_config.flags |= SDMMC_SLOT_FLAG_INTERNAL_PULLUP;  // 启用内部上拉电阻

    // 挂载SD卡
    esp_err_t ret = esp_vfs_fat_sdmmc_mount(CACHE_MOUNT_POINT, &host, &slot_config, &mount_config, &g_card);
    if (ret != ESP_OK) {
        if (ret == ESP_FAIL) {
            ESP_LOGE(TAG, "Failed to mount filesystem. Card may need formatting or be corrupted.");
        } else {
            ESP_LOGE(TAG, "Failed to initialize the card (%s). Check SD card connection and pull-up resistors.", esp_err_to_name(ret));
        }
        return ret;
    }

    ESP_LOGI(TAG, "SD card mounted successfully at %s", CACHE_MOUNT_POINT);

    // 创建缓存目录
    ret = _create_cache_directory();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to create cache directory");
        esp_vfs_fat_sdcard_unmount(CACHE_MOUNT_POINT, g_card);
        return ret;
    }

    // 检查文件系统权限
    ret = _check_filesystem_permissions();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Filesystem permission check failed");
        esp_vfs_fat_sdcard_unmount(CACHE_MOUNT_POINT, g_card);
        return ret;
    }

    // 打印SD卡信息和空间信息
    sdmmc_card_print_info(stdout, g_card);

    // 检查SD卡状态
    ESP_LOGI(TAG, "SD Card Status:");
    ESP_LOGI(TAG, "  Bus Width: %d-bit", g_card->log_bus_width);
    ESP_LOGI(TAG, "  Frequency: %d kHz", g_card->real_freq_khz);
    ESP_LOGI(TAG, "  Capacity: %llu MB",
             ((uint64_t)g_card->csd.capacity) * g_card->csd.sector_size / (1024 * 1024));

    // 打印SD卡空间信息
    FATFS *fs;
    DWORD fre_clust, fre_sect, tot_sect;
    
    /* 获取卷信息和空闲簇数量 */
    FRESULT res = f_getfree("0:", &fre_clust, &fs);
    if (res == FR_OK) {
        /* 计算总扇区数和空闲扇区数 */
        tot_sect = (fs->n_fatent - 2) * fs->csize;
        fre_sect = fre_clust * fs->csize;
        
        /* 转换为MB */
        uint64_t total_mb = (uint64_t)tot_sect * 512 / (1024 * 1024);
        uint64_t free_mb = (uint64_t)fre_sect * 512 / (1024 * 1024);
        uint64_t used_mb = total_mb - free_mb;
        
        printf("\n--- SD Card Space Information ---\n");
        printf("Total Space: %llu MB\n", total_mb);
        printf("Used Space:  %llu MB\n", used_mb);
        printf("Free Space:  %llu MB\n", free_mb);
        printf("Usage:       %.1f%%\n", (float)used_mb / total_mb * 100);
        printf("--------------------------------\n\n");
    } else {
        printf("Failed to get SD card space info: %d\n", res);
    }

    #if ENABLE_SONG_CACHE
    set_cache_enabled(true);
    #endif

    // 扫描现有缓存文件
    ret = _scan_cache_files();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to scan cache files");
        esp_vfs_fat_sdcard_unmount(CACHE_MOUNT_POINT, g_card);
        return ret;
    }

    g_cache_manager.initialized = true;
    ESP_LOGI(TAG, "Cache system initialized successfully. Files: %d, Size: %llu MB",
             g_cache_manager.file_count, g_cache_manager.total_cache_size / (1024 * 1024));

    // 运行文件创建测试
    ret = song_cache_test_file_creation();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "File creation test failed, but continuing with initialization");
        // 不返回错误，允许系统继续运行，但会记录问题
    }

    return ESP_OK;
}

/**
 * SD卡缓存系统反初始化
 */
esp_err_t song_cache_deinit(void)
{
    if (!g_cache_manager.initialized) {
        return ESP_OK;
    }

    // 清理文件列表
    cache_file_info_t *current = g_cache_manager.file_list_head;
    while (current) {
        cache_file_info_t *next = current->next;
        free(current);
        current = next;
    }

    // 卸载SD卡
    esp_err_t ret = esp_vfs_fat_sdcard_unmount(CACHE_MOUNT_POINT, g_card);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to unmount SD card");
    }

    memset(&g_cache_manager, 0, sizeof(g_cache_manager));
    g_card = NULL;

    ESP_LOGI(TAG, "Cache system deinitialized");
    return ret;
}

/**
 * 检查歌曲是否已缓存
 */
bool song_cache_exists(const char *md5)
{
    if (!g_cache_manager.initialized || !md5) {
        return false;
    }

    char filepath[256];
    _generate_cache_filepath(md5, filepath, sizeof(filepath));

    struct stat st;
    return (stat(filepath, &st) == 0);
}

/**
 * 获取SD卡剩余空间
 */
uint64_t song_cache_get_free_space(void)
{
    if (!g_cache_manager.initialized) {
        return 0;
    }

    FATFS *fs;
    DWORD fre_clust;
    
    if (f_getfree("0:", &fre_clust, &fs) == FR_OK) {
        uint64_t free_bytes = (uint64_t)fre_clust * fs->csize * 512;
        return free_bytes;
    }
    
    return 0;
}

/**
 * 获取缓存已使用空间
 */
uint64_t song_cache_get_used_space(void)
{
    return g_cache_manager.total_cache_size;
}

/**
 * 开始写入缓存文件
 */
esp_err_t song_cache_start_write(const char *md5, uint32_t sample_rate, 
                                uint32_t channels, uint32_t total_frames, 
                                uint32_t song_length, cache_write_handle_t *handle)
{
    if (!g_cache_manager.initialized || !md5 || !handle) {
        return ESP_ERR_INVALID_ARG;
    }

    // 检查是否已存在
    if (song_cache_exists(md5)) {
        ESP_LOGW(TAG, "Cache file already exists for MD5: %s", md5);
        return ESP_ERR_INVALID_STATE;
    }

    // 生成文件路径
    _generate_cache_filepath(md5, handle->filepath, sizeof(handle->filepath));

    // 打开文件
    handle->file = fopen(handle->filepath, "wb");
    if (!handle->file) {
        ESP_LOGE(TAG, "Failed to create cache file: %s (errno: %d, %s)",
                 handle->filepath, errno, strerror(errno));

        // 检查磁盘空间
        uint64_t free_space = song_cache_get_free_space();
        ESP_LOGE(TAG, "Available free space: %llu bytes", free_space);

        // 检查目录权限
        struct stat dir_stat;
        if (stat(CACHE_DIR_PATH, &dir_stat) == 0) {
            ESP_LOGE(TAG, "Cache directory permissions: 0%o", dir_stat.st_mode & 0777);
        }

        return ESP_FAIL;
    }

    // 初始化句柄
    strncpy(handle->md5, md5, sizeof(handle->md5) - 1);
    handle->md5[sizeof(handle->md5) - 1] = '\0';
    handle->frames_written = 0;
    handle->header_written = false;

    // 准备文件头
    memset(&handle->header, 0, sizeof(handle->header));
    handle->header.magic = CACHE_FILE_MAGIC;
    handle->header.version = CACHE_FILE_VERSION;
    handle->header.timestamp = time(NULL);
    handle->header.sample_rate = sample_rate;
    handle->header.channels = channels;
    handle->header.total_frames = total_frames;
    handle->header.song_length = song_length;
    strncpy(handle->header.md5, md5, sizeof(handle->header.md5) - 1);
    handle->header.frame_count = 0;  // 将在写入完成时更新

    // 写入文件头（预留空间，稍后更新）
    if (fwrite(&handle->header, sizeof(handle->header), 1, handle->file) != 1) {
        ESP_LOGE(TAG, "Failed to write cache file header");
        fclose(handle->file);
        remove(handle->filepath);
        return ESP_FAIL;
    }

    handle->header_written = true;
    ESP_LOGI(TAG, "Started writing cache file: %s", md5);

    return ESP_OK;
}

/**
 * 写入音频帧到缓存文件
 */
esp_err_t song_cache_write_frame(cache_write_handle_t *handle, 
                                const uint8_t *frame_data, uint32_t frame_size)
{
    if (!handle || !handle->file || !frame_data || frame_size == 0) {
        return ESP_ERR_INVALID_ARG;
    }

    // 写入帧头（帧大小）
    audio_frame_header_t frame_header = { .frame_size = frame_size };
    if (fwrite(&frame_header, sizeof(frame_header), 1, handle->file) != 1) {
        ESP_LOGE(TAG, "Failed to write frame header");
        return ESP_FAIL;
    }

    // 写入帧数据
    if (fwrite(frame_data, 1, frame_size, handle->file) != frame_size) {
        ESP_LOGE(TAG, "Failed to write frame data");
        return ESP_FAIL;
    }

    handle->frames_written++;
    return ESP_OK;
}

/**
 * 完成缓存文件写入
 */
esp_err_t song_cache_finish_write(cache_write_handle_t *handle)
{
    if (!handle || !handle->file) {
        return ESP_ERR_INVALID_ARG;
    }

    // 更新文件头中的帧数量
    handle->header.frame_count = handle->frames_written;
    
    // 回到文件开头更新头部
    if (fseek(handle->file, 0, SEEK_SET) != 0) {
        ESP_LOGE(TAG, "Failed to seek to file beginning");
        fclose(handle->file);
        remove(handle->filepath);
        return ESP_FAIL;
    }

    if (fwrite(&handle->header, sizeof(handle->header), 1, handle->file) != 1) {
        ESP_LOGE(TAG, "Failed to update file header");
        fclose(handle->file);
        remove(handle->filepath);
        return ESP_FAIL;
    }

    fclose(handle->file);
    handle->file = NULL;

    // 获取文件大小并添加到缓存列表
    struct stat st;
    if (stat(handle->filepath, &st) == 0) {
        _add_file_to_list(handle->md5, handle->header.timestamp, st.st_size);
        g_cache_manager.total_cache_size += st.st_size;
        g_cache_manager.file_count++;
    }

    ESP_LOGI(TAG, "Finished writing cache file: %s (%d frames)", handle->md5, handle->frames_written);
    return ESP_OK;
}

/**
 * 取消缓存文件写入
 */
esp_err_t song_cache_cancel_write(cache_write_handle_t *handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    if (handle->file) {
        fclose(handle->file);
        handle->file = NULL;
    }

    // 删除未完成的文件
    remove(handle->filepath);
    
    ESP_LOGI(TAG, "Cancelled writing cache file: %s", handle->md5);
    return ESP_OK;
}

/**
 * 创建缓存目录
 */
static esp_err_t _create_cache_directory(void)
{
    struct stat st = {0};

    // 首先检查挂载点是否可访问
    if (stat(CACHE_MOUNT_POINT, &st) == -1) {
        ESP_LOGE(TAG, "SD card mount point not accessible: %s", strerror(errno));
        return ESP_FAIL;
    }

    // 检查缓存目录是否存在
    if (stat(CACHE_DIR_PATH, &st) == -1) {
        // 目录不存在，创建它
        ESP_LOGI(TAG, "Creating cache directory: %s", CACHE_DIR_PATH);
        if (mkdir(CACHE_DIR_PATH, 0755) != 0) {
            ESP_LOGE(TAG, "Failed to create cache directory: %s (errno: %d)",
                     strerror(errno), errno);
            return ESP_FAIL;
        }
        ESP_LOGI(TAG, "Cache directory created successfully");
    } else {
        // 检查是否为目录
        if (!S_ISDIR(st.st_mode)) {
            ESP_LOGE(TAG, "Cache path exists but is not a directory");
            return ESP_FAIL;
        }
        ESP_LOGI(TAG, "Cache directory already exists");
    }

    // 测试文件系统是否可写
    char test_file_path[256];
    snprintf(test_file_path, sizeof(test_file_path), "%s/.test_write", CACHE_DIR_PATH);

    FILE *test_file = fopen(test_file_path, "w");
    if (!test_file) {
        ESP_LOGE(TAG, "SD card filesystem is not writable: %s", strerror(errno));
        return ESP_FAIL;
    }

    // 写入测试数据
    const char *test_data = "test";
    if (fwrite(test_data, 1, strlen(test_data), test_file) != strlen(test_data)) {
        ESP_LOGE(TAG, "Failed to write test data to SD card");
        fclose(test_file);
        remove(test_file_path);
        return ESP_FAIL;
    }

    fclose(test_file);

    // 验证文件是否可读
    test_file = fopen(test_file_path, "r");
    if (!test_file) {
        ESP_LOGE(TAG, "Failed to read back test file");
        remove(test_file_path);
        return ESP_FAIL;
    }

    char read_buffer[16];
    size_t read_size = fread(read_buffer, 1, sizeof(read_buffer) - 1, test_file);
    fclose(test_file);
    remove(test_file_path);  // 清理测试文件

    if (read_size != strlen(test_data) || strncmp(read_buffer, test_data, strlen(test_data)) != 0) {
        ESP_LOGE(TAG, "Test file data verification failed");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "Cache directory ready and writable: %s", CACHE_DIR_PATH);
    return ESP_OK;
}

/**
 * 扫描现有缓存文件
 */
static esp_err_t _scan_cache_files(void)
{
    DIR *dir = opendir(CACHE_DIR_PATH);
    if (!dir) {
        ESP_LOGE(TAG, "Failed to open cache directory");
        return ESP_FAIL;
    }

    struct dirent *entry;
    g_cache_manager.total_cache_size = 0;
    g_cache_manager.file_count = 0;
    int deleted_files = 0;

    while ((entry = readdir(dir)) != NULL) {
        if (entry->d_type == DT_REG) {  // 普通文件
            char filepath[384];
            snprintf(filepath, sizeof(filepath), "%s/%s", CACHE_DIR_PATH, entry->d_name);
            
            struct stat st;
            if (stat(filepath, &st) == 0) {
                // 从文件名提取MD5（去掉.cache扩展名）
                char *dot = strrchr(entry->d_name, '.');
                if (dot && strcmp(dot, ".cache") == 0) {
                    // 验证缓存文件完整性
                    bool is_valid = true;
                    
                    // 检查文件大小是否至少包含文件头
                    if (st.st_size < sizeof(cache_file_header_t)) {
                        ESP_LOGW(TAG, "Cache file too small, deleting: %s (size: %ld)", filepath, st.st_size);
                        is_valid = false;
                    } else {
                        // 尝试读取并验证文件头
                        FILE *file = fopen(filepath, "rb");
                        if (file) {
                            cache_file_header_t header;
                            if (fread(&header, sizeof(header), 1, file) == 1) {
                                // 检查魔数和版本号
                                if (header.magic != CACHE_FILE_MAGIC) {
                                    ESP_LOGW(TAG, "Invalid magic number in cache file, deleting: %s (magic: 0x%08X)", 
                                             filepath, header.magic);
                                    is_valid = false;
                                } else if (header.version != CACHE_FILE_VERSION) {
                                    ESP_LOGW(TAG, "Unsupported cache file version, deleting: %s (version: %d)", 
                                             filepath, header.version);
                                    is_valid = false;
                                } else {
                                    // 检查文件大小是否合理（至少包含头部和一些音频数据）
                                    uint64_t expected_min_size = sizeof(cache_file_header_t) + 
                                                                sizeof(audio_frame_header_t) * header.frame_count;
                                    if (st.st_size < expected_min_size || header.frame_count == 0) {
                                        ESP_LOGW(TAG, "Cache file appears incomplete, deleting: %s (size: %ld, expected min: %llu)", 
                                                 filepath, st.st_size, expected_min_size);
                                        is_valid = false;
                                    }
                                }
                            } else {
                                ESP_LOGW(TAG, "Failed to read cache file header, deleting: %s", filepath);
                                is_valid = false;
                            }
                            fclose(file);
                        } else {
                            ESP_LOGW(TAG, "Failed to open cache file for validation, deleting: %s", filepath);
                            is_valid = false;
                        }
                    }
                    
                    if (!is_valid) {
                        // 删除不完整或损坏的缓存文件
                        if (remove(filepath) == 0) {
                            ESP_LOGI(TAG, "Deleted incomplete cache file: %s", filepath);
                            deleted_files++;
                        } else {
                            ESP_LOGE(TAG, "Failed to delete incomplete cache file: %s", filepath);
                        }
                    } else {
                        // 文件有效，添加到缓存列表
                        *dot = '\0';  // 临时去掉扩展名
                        _add_file_to_list(entry->d_name, st.st_mtime, st.st_size);
                        g_cache_manager.total_cache_size += st.st_size;
                        g_cache_manager.file_count++;
                    }
                }
            }
        }
    }

    closedir(dir);
    
    if (deleted_files > 0) {
        ESP_LOGI(TAG, "Cleaned up %d incomplete cache files", deleted_files);
    }
    
    ESP_LOGI(TAG, "Scanned %d valid cache files, total size: %llu bytes", 
             g_cache_manager.file_count, g_cache_manager.total_cache_size);
    
    return ESP_OK;
}

/**
 * 添加文件到缓存列表
 */
static esp_err_t _add_file_to_list(const char *filename, uint64_t timestamp, uint32_t file_size)
{
    cache_file_info_t *info = malloc(sizeof(cache_file_info_t));
    if (!info) {
        return ESP_ERR_NO_MEM;
    }

    strncpy(info->filename, filename, sizeof(info->filename) - 1);
    info->filename[sizeof(info->filename) - 1] = '\0';
    strncpy(info->md5, filename, sizeof(info->md5) - 1);
    info->md5[sizeof(info->md5) - 1] = '\0';
    info->timestamp = timestamp;
    info->file_size = file_size;
    info->next = g_cache_manager.file_list_head;
    
    g_cache_manager.file_list_head = info;
    return ESP_OK;
}

/**
 * 开始读取缓存文件
 */
esp_err_t song_cache_start_read(const char *md5, cache_read_handle_t *handle)
{
    if (!g_cache_manager.initialized || !md5 || !handle) {
        return ESP_ERR_INVALID_ARG;
    }

    char filepath[256];
    _generate_cache_filepath(md5, filepath, sizeof(filepath));

    // 打开文件
    handle->file = fopen(filepath, "rb");
    if (!handle->file) {
        ESP_LOGE(TAG, "Failed to open cache file: %s", filepath);
        return ESP_FAIL;
    }

    // 读取文件头
    if (fread(&handle->header, sizeof(handle->header), 1, handle->file) != 1) {
        ESP_LOGE(TAG, "Failed to read cache file header");
        fclose(handle->file);
        return ESP_FAIL;
    }

    // 验证文件头
    if (handle->header.magic != CACHE_FILE_MAGIC) {
        ESP_LOGE(TAG, "Invalid cache file magic number");
        fclose(handle->file);
        return ESP_FAIL;
    }

    if (strcmp(handle->header.md5, md5) != 0) {
        ESP_LOGE(TAG, "MD5 mismatch in cache file");
        fclose(handle->file);
        return ESP_FAIL;
    }

    handle->current_frame = 0;
    handle->header_read = true;

    ESP_LOGI(TAG, "Started reading cache file: %s (%d frames)", md5, handle->header.frame_count);
    return ESP_OK;
}

/**
 * 读取音频帧从缓存文件
 */
esp_err_t song_cache_read_frame(cache_read_handle_t *handle, 
                               uint8_t *frame_data, uint32_t *frame_size)
{
    if (!handle || !handle->file || !frame_data || !frame_size) {
        return ESP_ERR_INVALID_ARG;
    }

    if (handle->current_frame >= handle->header.frame_count) {
        return ESP_ERR_NOT_FOUND;  // 已读取完所有帧
    }

    // 读取帧头
    audio_frame_header_t frame_header;
    if (fread(&frame_header, sizeof(frame_header), 1, handle->file) != 1) {
        ESP_LOGE(TAG, "Failed to read frame header");
        return ESP_FAIL;
    }

    // 检查帧大小是否合理
    if (frame_header.frame_size == 0 || frame_header.frame_size > 64 * 1024) {
        ESP_LOGE(TAG, "Invalid frame size: %d", frame_header.frame_size);
        return ESP_FAIL;
    }

    // 读取帧数据
    if (fread(frame_data, 1, frame_header.frame_size, handle->file) != frame_header.frame_size) {
        ESP_LOGE(TAG, "Failed to read frame data");
        return ESP_FAIL;
    }

    *frame_size = frame_header.frame_size;
    handle->current_frame++;

    return ESP_OK;
}

/**
 * 完成缓存文件读取
 */
esp_err_t song_cache_finish_read(cache_read_handle_t *handle)
{
    if (!handle) {
        return ESP_ERR_INVALID_ARG;
    }

    if (handle->file) {
        fclose(handle->file);
        handle->file = NULL;
    }

    ESP_LOGI(TAG, "Finished reading cache file");
    return ESP_OK;
}

/**
 * 清理缓存空间
 */
esp_err_t song_cache_cleanup_space(uint64_t required_space)
{
    if (!g_cache_manager.initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    uint64_t free_space = song_cache_get_free_space();
    uint64_t total_required = required_space + (MIN_FREE_SPACE_MB * 1024 * 1024);

    ESP_LOGI(TAG, "Cleanup check: free=%llu MB, required=%llu MB", 
             free_space / (1024 * 1024), total_required / (1024 * 1024));

    while (free_space < total_required && g_cache_manager.file_count > 0) {
        cache_file_info_t *oldest = _find_oldest_file();
        if (!oldest) {
            break;
        }

        ESP_LOGI(TAG, "Deleting oldest cache file: %s", oldest->md5);
        
        // 删除文件
        char filepath[256];
        _generate_cache_filepath(oldest->md5, filepath, sizeof(filepath));
        
        if (remove(filepath) == 0) {
            g_cache_manager.total_cache_size -= oldest->file_size;
            g_cache_manager.file_count--;
            _remove_file_from_list(oldest->md5);
            
            free_space = song_cache_get_free_space();
            ESP_LOGI(TAG, "Deleted cache file, new free space: %llu MB", free_space / (1024 * 1024));
        } else {
            ESP_LOGE(TAG, "Failed to delete cache file: %s", filepath);
            break;
        }
    }

    return ESP_OK;
}

/**
 * 删除指定的缓存文件
 */
esp_err_t song_cache_delete_file(const char *md5)
{
    if (!g_cache_manager.initialized || !md5) {
        return ESP_ERR_INVALID_ARG;
    }

    char filepath[256];
    _generate_cache_filepath(md5, filepath, sizeof(filepath));

    struct stat st;
    if (stat(filepath, &st) == 0) {
        if (remove(filepath) == 0) {
            g_cache_manager.total_cache_size -= st.st_size;
            g_cache_manager.file_count--;
            _remove_file_from_list(md5);
            ESP_LOGI(TAG, "Deleted cache file: %s", md5);
            return ESP_OK;
        }
    }

    return ESP_FAIL;
}

/**
 * 获取缓存文件信息
 */
esp_err_t song_cache_get_file_info(const char *md5, cache_file_header_t *header)
{
    if (!g_cache_manager.initialized || !md5 || !header) {
        return ESP_ERR_INVALID_ARG;
    }

    char filepath[256];
    _generate_cache_filepath(md5, filepath, sizeof(filepath));

    FILE *file = fopen(filepath, "rb");
    if (!file) {
        return ESP_ERR_NOT_FOUND;
    }

    esp_err_t ret = ESP_OK;
    if (fread(header, sizeof(*header), 1, file) != 1) {
        ret = ESP_FAIL;
    } else if (header->magic != CACHE_FILE_MAGIC) {
        ret = ESP_ERR_INVALID_CRC;
    }

    fclose(file);
    return ret;
}

/**
 * 从文件列表中移除文件
 */
static esp_err_t _remove_file_from_list(const char *md5)
{
    cache_file_info_t **current = &g_cache_manager.file_list_head;
    
    while (*current) {
        if (strcmp((*current)->md5, md5) == 0) {
            cache_file_info_t *to_remove = *current;
            *current = (*current)->next;
            free(to_remove);
            return ESP_OK;
        }
        current = &(*current)->next;
    }
    
    return ESP_ERR_NOT_FOUND;
}

/**
 * 查找最旧的文件
 */
static cache_file_info_t* _find_oldest_file(void)
{
    cache_file_info_t *oldest = NULL;
    cache_file_info_t *current = g_cache_manager.file_list_head;
    
    while (current) {
        if (!oldest || current->timestamp < oldest->timestamp) {
            oldest = current;
        }
        current = current->next;
    }
    
    return oldest;
}

/**
 * 检查SD卡是否为只读模式
 */
static bool _is_sdcard_readonly(void)
{
    // 通过尝试创建文件来检测是否只读
    char test_path[256];
    snprintf(test_path, sizeof(test_path), "%s/readonly_test.tmp", CACHE_MOUNT_POINT);

    ESP_LOGI(TAG, "Testing file creation at: %s", test_path);

    FILE *test_file = fopen(test_path, "w");
    if (!test_file) {
        ESP_LOGW(TAG, "Cannot create test file, SD card may be read-only: %s (errno: %d)",
                 strerror(errno), errno);

        // 尝试不同的文件名
        snprintf(test_path, sizeof(test_path), "%s/test.txt", CACHE_MOUNT_POINT);
        test_file = fopen(test_path, "w");
        if (!test_file) {
            ESP_LOGW(TAG, "Second attempt also failed: %s (errno: %d)",
                     strerror(errno), errno);
            return true;  // 无法创建文件，可能是只读
        }
    }

    // 尝试写入数据
    if (fwrite("test", 1, 4, test_file) != 4) {
        ESP_LOGW(TAG, "Cannot write to test file, SD card may be read-only");
        fclose(test_file);
        remove(test_path);
        return true;
    }

    fclose(test_file);

    // 尝试删除文件
    if (remove(test_path) != 0) {
        ESP_LOGW(TAG, "Cannot remove test file, SD card may be read-only");
        return true;
    }

    return false;  // 可以创建、写入和删除文件，不是只读
}

/**
 * 诊断SD卡问题
 */
static void _diagnose_sdcard_issues(void)
{
    ESP_LOGI(TAG, "=== SD Card Diagnostic Information ===");

    // 检查挂载点
    struct stat mount_stat;
    if (stat(CACHE_MOUNT_POINT, &mount_stat) == 0) {
        ESP_LOGI(TAG, "Mount point exists: %s", CACHE_MOUNT_POINT);
        ESP_LOGI(TAG, "Mount point permissions: 0%o", mount_stat.st_mode & 0777);
        ESP_LOGI(TAG, "Mount point is %s", S_ISDIR(mount_stat.st_mode) ? "directory" : "not directory");
    } else {
        ESP_LOGE(TAG, "Mount point does not exist: %s", CACHE_MOUNT_POINT);
    }

    // 检查缓存目录
    struct stat cache_stat;
    if (stat(CACHE_DIR_PATH, &cache_stat) == 0) {
        ESP_LOGI(TAG, "Cache directory exists: %s", CACHE_DIR_PATH);
        ESP_LOGI(TAG, "Cache directory permissions: 0%o", cache_stat.st_mode & 0777);
    } else {
        ESP_LOGE(TAG, "Cache directory does not exist: %s", CACHE_DIR_PATH);
    }

    // 检查磁盘空间
    uint64_t free_space = song_cache_get_free_space();
    ESP_LOGI(TAG, "Free space: %llu bytes (%llu MB)", free_space, free_space / (1024 * 1024));

    // 检查SD卡信息
    if (g_card) {
        ESP_LOGI(TAG, "SD Card present:");
        ESP_LOGI(TAG, "  Sector size: %d bytes", g_card->csd.sector_size);
        ESP_LOGI(TAG, "  Capacity: %llu sectors", (uint64_t)g_card->csd.capacity);
        ESP_LOGI(TAG, "  Bus width: %d", g_card->log_bus_width);
        ESP_LOGI(TAG, "  Frequency: %d kHz", g_card->real_freq_khz);
    } else {
        ESP_LOGE(TAG, "No SD card information available");
    }

    ESP_LOGI(TAG, "=== End of SD Card Diagnostic ===");
}

/**
 * 测试SD卡文件创建功能
 */
esp_err_t song_cache_test_file_creation(void)
{
    if (!g_cache_manager.initialized) {
        ESP_LOGE(TAG, "Cache system not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "Testing SD card file creation...");

    // 创建测试文件
    char test_filepath[256];
    snprintf(test_filepath, sizeof(test_filepath), "%s/test_creation_%lu.tmp",
             CACHE_DIR_PATH, (unsigned long)time(NULL));

    FILE *test_file = fopen(test_filepath, "wb");
    if (!test_file) {
        ESP_LOGE(TAG, "Failed to create test file: %s (errno: %d, %s)",
                 test_filepath, errno, strerror(errno));
        _diagnose_sdcard_issues();
        return ESP_FAIL;
    }

    // 写入测试数据
    const char *test_data = "This is a test file for SD card functionality";
    size_t data_len = strlen(test_data);

    if (fwrite(test_data, 1, data_len, test_file) != data_len) {
        ESP_LOGE(TAG, "Failed to write test data: %s", strerror(errno));
        fclose(test_file);
        remove(test_filepath);
        return ESP_FAIL;
    }

    fclose(test_file);

    // 验证文件存在
    struct stat st;
    if (stat(test_filepath, &st) != 0) {
        ESP_LOGE(TAG, "Test file does not exist after creation");
        return ESP_FAIL;
    }

    // 验证文件大小
    if (st.st_size != data_len) {
        ESP_LOGE(TAG, "Test file size mismatch: expected %zu, got %ld",
                 data_len, st.st_size);
        remove(test_filepath);
        return ESP_FAIL;
    }

    // 读取并验证数据
    test_file = fopen(test_filepath, "rb");
    if (!test_file) {
        ESP_LOGE(TAG, "Failed to reopen test file for reading");
        remove(test_filepath);
        return ESP_FAIL;
    }

    char read_buffer[256];
    size_t read_size = fread(read_buffer, 1, sizeof(read_buffer) - 1, test_file);
    fclose(test_file);

    if (read_size != data_len || memcmp(read_buffer, test_data, data_len) != 0) {
        ESP_LOGE(TAG, "Test file data verification failed");
        remove(test_filepath);
        return ESP_FAIL;
    }

    // 清理测试文件
    if (remove(test_filepath) != 0) {
        ESP_LOGW(TAG, "Failed to remove test file: %s", strerror(errno));
    }

    ESP_LOGI(TAG, "SD card file creation test passed successfully");
    return ESP_OK;
}

/**
 * 生成缓存文件路径
 */
static void _generate_cache_filepath(const char *md5, char *filepath, size_t filepath_size)
{
    snprintf(filepath, filepath_size, "%s/%s.cache", CACHE_DIR_PATH, md5);
}