#!/usr/bin/env python3
"""
简单的C语法检查脚本
检查主要的C文件是否有明显的语法错误
"""

import os
import re

def check_c_file(filepath):
    """检查C文件的基本语法"""
    print(f"检查文件: {filepath}")
    
    if not os.path.exists(filepath):
        print(f"  ✗ 文件不存在")
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"  ✗ 读取文件失败: {e}")
        return False
    
    issues = []
    
    # 检查基本的括号匹配
    open_braces = content.count('{')
    close_braces = content.count('}')
    if open_braces != close_braces:
        issues.append(f"大括号不匹配: 开 {open_braces}, 闭 {close_braces}")

    open_parens = content.count('(')
    close_parens = content.count(')')
    if open_parens != close_parens:
        issues.append(f"小括号不匹配: 开 {open_parens}, 闭 {close_parens}")
    
    # 检查include语句
    includes = re.findall(r'#include\s*[<"][^>"]+[>"]', content)
    print(f"  包含文件数量: {len(includes)}")
    
    # 检查函数定义
    functions = re.findall(r'\w+\s+\w+\s*\([^)]*\)\s*{', content)
    print(f"  函数定义数量: {len(functions)}")
    
    if issues:
        print(f"  ✗ 发现问题:")
        for issue in issues:
            print(f"    - {issue}")
        return False
    else:
        print(f"  ✓ 基本语法检查通过")
        return True

def main():
    """主函数"""
    print("C语法检查工具")
    print("=" * 50)
    
    # 要检查的文件列表
    files_to_check = [
        "main/softap_config.c",
        "main/softap_config.h",
        "main/simple_page.c",
        "main/simple_page.h",
        "main/main.c"
    ]
    
    passed = 0
    total = len(files_to_check)
    
    for filepath in files_to_check:
        if check_c_file(filepath):
            passed += 1
        print()
    
    print("=" * 50)
    print(f"检查结果: {passed}/{total} 文件通过基本语法检查")
    
    if passed == total:
        print("✓ 所有文件都通过了基本语法检查")
        print("建议使用ESP-IDF工具链进行完整编译测试")
    else:
        print("✗ 部分文件存在语法问题，请修复后重试")

if __name__ == "__main__":
    main()
