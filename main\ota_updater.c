#include "esp_https_ota.h"
#include "esp_log.h"
#include "sysconf.h"
#include "network.h"
#include "ota_updater.h"

static const char *TAG = "OTA";

bool isUpdateing = false;

esp_err_t ota_init() {
    // 初始化必要的OTA配置
    return ESP_OK;
}

void ota_start(void *url) {
    char *ota_url = (char *)url;
    esp_err_t ret = ESP_OK;
    esp_http_client_config_t config = {
        .url = ota_url,
        .auth_type = HTTP_AUTH_TYPE_NONE,
        .method = HTTP_METHOD_GET,
        .cert_pem = NULL,  // 根据服务器证书情况配置
        .skip_cert_common_name_check = true,  // 添加此参数跳过验证
        .timeout_ms = 30000
    };
    
    esp_https_ota_config_t ota_config = {
        .http_config = &config,
    };

    esp_https_ota_handle_t https_ota_handle = NULL;
    ESP_LOGI(TAG, "Starting OTA update from: %s", ota_url);

    esp_err_t ota_finish_err = ESP_OK;
    ret = esp_https_ota_begin(&ota_config, &https_ota_handle);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "ESP HTTPS OTA Begin failed");
        goto ota_end;
    }

    esp_app_desc_t app_desc;
    ret = esp_https_ota_get_img_desc(https_ota_handle, &app_desc);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "esp_https_ota_get_img_desc failed");
        goto ota_end;
    }

    while (1) {
        ret = esp_https_ota_perform(https_ota_handle);
        if (ret != ESP_ERR_HTTPS_OTA_IN_PROGRESS) {
            break;
        }
    }

    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "OTA update successful. Restarting...");
        ota_finish_err = esp_https_ota_finish(https_ota_handle);
        if (ota_finish_err == ESP_OK) {
            vTaskDelay(1000 / portTICK_PERIOD_MS);
            System_Reboot(false);
        }
    } else {
        ESP_LOGE(TAG, "OTA failed: 0x%x", ret);
    }

ota_end:
    esp_https_ota_abort(https_ota_handle);
    free(ota_url);
    isUpdateing=false;
    vTaskDelete(NULL);
}

void start_ota_task(char *url) {
    // 从参数中获取URL
    if(isUpdateing)
    {
        ESP_LOGI(TAG, "OTA is already running");
        return;
    }
    isUpdateing=true;
    char *url_copy = strdup(url);
    xTaskCreate(ota_start, "ota task", 4096, url_copy, 3, NULL);
}
