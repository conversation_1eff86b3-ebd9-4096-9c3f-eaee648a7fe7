#!/usr/bin/env python3
"""
WiFi Speaker SoftAP API 接口测试示例
演示所有基础API接口的使用方法
"""

import requests
import json
import time
import sys

# 设备配置
DEVICE_IP = "************"
BASE_URL = f"http://{DEVICE_IP}"

def print_separator(title):
    """打印分隔符"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def print_request_info(method, url, data=None):
    """打印请求信息"""
    print(f"\n📡 请求信息:")
    print(f"   方法: {method}")
    print(f"   URL: {url}")
    if data:
        print(f"   数据: {json.dumps(data, indent=2, ensure_ascii=False)}")

def print_response_info(response):
    """打印响应信息"""
    print(f"\n📥 响应信息:")
    print(f"   状态码: {response.status_code}")
    print(f"   内容类型: {response.headers.get('Content-Type', 'N/A')}")
    try:
        response_data = response.json()
        print(f"   响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
        return response_data
    except:
        print(f"   响应内容: {response.text}")
        return None

def test_device_info():
    """测试获取设备信息接口"""
    print_separator("1. 获取设备信息 API")
    
    url = f"{BASE_URL}/api/device/info"
    print_request_info("GET", url)
    
    try:
        response = requests.get(url, timeout=5)
        data = print_response_info(response)
        
        if response.status_code == 200 and data:
            print(f"\n✅ 设备信息获取成功:")
            print(f"   设备码: {data.get('data', {}).get('deviceCode', 'N/A')}")
            print(f"   设备名: {data.get('data', {}).get('deviceName', 'N/A')}")
            print(f"   固件版本: {data.get('data', {}).get('firmwareVersion', 'N/A')}")
            print(f"   MAC地址: {data.get('data', {}).get('macAddress', 'N/A')}")
            print(f"   WiFi状态: {'已连接' if data.get('data', {}).get('wifiConnected') else '未连接'}")
            return True
        else:
            print(f"\n❌ 设备信息获取失败")
            return False
    except Exception as e:
        print(f"\n❌ 请求失败: {e}")
        return False

def test_wifi_scan():
    """测试WiFi扫描接口"""
    print_separator("2. WiFi网络扫描 API")
    
    url = f"{BASE_URL}/api/wifi/scan"
    print_request_info("GET", url)
    
    try:
        response = requests.get(url, timeout=10)
        data = print_response_info(response)
        
        if response.status_code == 200 and data:
            wifi_list = data.get('data', [])
            print(f"\n✅ 扫描到 {len(wifi_list)} 个WiFi网络:")
            
            for i, wifi in enumerate(wifi_list[:5], 1):  # 只显示前5个
                ssid = wifi.get('ssid', 'Unknown')
                rssi = wifi.get('rssi', 0)
                auth_type = wifi.get('authType', 'Unknown')
                signal_strength = "强" if rssi > -50 else "中" if rssi > -70 else "弱"
                
                print(f"   {i}. {ssid}")
                print(f"      信号强度: {rssi}dBm ({signal_strength})")
                print(f"      加密类型: {auth_type}")
            
            if len(wifi_list) > 5:
                print(f"   ... 还有 {len(wifi_list) - 5} 个网络")
            
            return True
        else:
            print(f"\n❌ WiFi扫描失败")
            return False
    except Exception as e:
        print(f"\n❌ 请求失败: {e}")
        return False

def test_wifi_status():
    """测试WiFi状态查询接口"""
    print_separator("3. WiFi状态查询 API")
    
    url = f"{BASE_URL}/api/wifi/status"
    print_request_info("GET", url)
    
    try:
        response = requests.get(url, timeout=5)
        data = print_response_info(response)
        
        if response.status_code == 200 and data:
            status_data = data.get('data', {})
            status = status_data.get('status', 'unknown')
            connected = status_data.get('connected', False)
            
            status_map = {
                'idle': '空闲',
                'connecting': '连接中',
                'connected': '已连接',
                'failed': '连接失败'
            }
            
            print(f"\n✅ WiFi状态查询成功:")
            print(f"   连接状态: {status_map.get(status, status)}")
            print(f"   是否连接: {'是' if connected else '否'}")
            
            if connected:
                print(f"   当前SSID: {status_data.get('ssid', 'N/A')}")
                print(f"   信号强度: {status_data.get('rssi', 'N/A')}dBm")
                if 'ip' in status_data:
                    print(f"   IP地址: {status_data.get('ip')}")
            
            return True
        else:
            print(f"\n❌ WiFi状态查询失败")
            return False
    except Exception as e:
        print(f"\n❌ 请求失败: {e}")
        return False

def test_wifi_config():
    """测试WiFi配置接口"""
    print_separator("4. WiFi配置 API")
    
    print("⚠️  注意: 此测试将使用无效的WiFi配置进行演示")
    print("   实际使用时请替换为真实的WiFi信息")
    
    # 使用测试用的WiFi配置
    wifi_config = {
        "ssid": f"TEST_WIFI_{int(time.time())}",
        "password": "test_password_123"
    }
    
    url = f"{BASE_URL}/api/wifi/config"
    print_request_info("POST", url, wifi_config)
    
    try:
        response = requests.post(
            url,
            json=wifi_config,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        data = print_response_info(response)
        
        if response.status_code == 200:
            print(f"\n✅ WiFi配置请求发送成功")
            print(f"   消息: {data.get('message', 'N/A')}")
            
            # 演示状态轮询
            print(f"\n🔄 开始轮询连接状态...")
            for i in range(5):
                time.sleep(2)
                status_response = requests.get(f"{BASE_URL}/api/wifi/status", timeout=3)
                if status_response.status_code == 200:
                    status_data = status_response.json().get('data', {})
                    status = status_data.get('status', 'unknown')
                    print(f"   轮询 {i+1}: 状态 = {status}")
                    
                    if status == 'failed':
                        print(f"   ✅ 检测到连接失败（符合预期，因为使用了无效WiFi）")
                        break
                else:
                    print(f"   轮询 {i+1}: 状态查询失败")
            
            return True
        else:
            print(f"\n❌ WiFi配置失败")
            return False
    except Exception as e:
        print(f"\n❌ 请求失败: {e}")
        return False

def test_real_wifi_config():
    """测试真实WiFi配置（可选）"""
    print_separator("5. 真实WiFi配置测试（可选）")
    
    print("此测试允许您配置真实的WiFi网络")
    ssid = input("请输入WiFi名称（留空跳过）: ").strip()
    
    if not ssid:
        print("跳过真实WiFi配置测试")
        return True
    
    password = input("请输入WiFi密码: ").strip()
    
    wifi_config = {
        "ssid": ssid,
        "password": password
    }
    
    url = f"{BASE_URL}/api/wifi/config"
    print_request_info("POST", url, {"ssid": ssid, "password": "***"})
    
    try:
        response = requests.post(
            url,
            json=wifi_config,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        data = print_response_info(response)
        
        if response.status_code == 200:
            print(f"\n✅ 真实WiFi配置请求发送成功")
            
            # 监控连接状态
            print(f"\n🔄 监控连接状态（最多30秒）...")
            for i in range(15):
                time.sleep(2)
                try:
                    status_response = requests.get(f"{BASE_URL}/api/wifi/status", timeout=3)
                    if status_response.status_code == 200:
                        status_data = status_response.json().get('data', {})
                        status = status_data.get('status', 'unknown')
                        connected = status_data.get('connected', False)
                        
                        print(f"   轮询 {i+1}: 状态 = {status}, 连接 = {connected}")
                        
                        if connected:
                            print(f"\n🎉 WiFi连接成功!")
                            print(f"   SSID: {status_data.get('ssid', 'N/A')}")
                            print(f"   信号强度: {status_data.get('rssi', 'N/A')}dBm")
                            if 'ip' in status_data:
                                print(f"   IP地址: {status_data.get('ip')}")
                            print(f"\n⚠️  设备热点即将关闭，请切换到您的WiFi网络")
                            break
                        elif status == 'failed':
                            print(f"\n❌ WiFi连接失败，请检查密码是否正确")
                            break
                    else:
                        print(f"   轮询 {i+1}: 状态查询失败")
                except:
                    print(f"   轮询 {i+1}: 网络错误（可能设备已切换网络）")
                    if i > 5:  # 如果查询多次失败，可能是连接成功了
                        print(f"\n✅ 设备可能已成功连接WiFi（热点已关闭）")
                        break
            
            return True
        else:
            print(f"\n❌ 真实WiFi配置失败")
            return False
    except Exception as e:
        print(f"\n❌ 请求失败: {e}")
        return False

def test_system_reset():
    """测试系统重置接口"""
    print_separator("6. 系统重置 API（演示）")
    
    print("⚠️  注意: 此接口会重启设备，仅作演示，不会实际执行")
    
    url = f"{BASE_URL}/api/system/reset"
    print_request_info("POST", url)
    
    print(f"\n📝 如果要实际执行重置，请使用以下命令:")
    print(f"   curl -X POST {url}")
    
    return True

def main():
    """主函数"""
    print("🚀 WiFi Speaker SoftAP API 接口测试")
    print("📖 本脚本演示所有基础API接口的使用方法")
    
    # 检查设备连接
    print_separator("连接检查")
    try:
        response = requests.get(f"{BASE_URL}/api/device/info", timeout=3)
        if response.status_code == 200:
            print("✅ 设备连接正常")
        else:
            print("❌ 设备响应异常")
            return
    except:
        print("❌ 无法连接到设备，请确认：")
        print("   1. 已连接到设备热点 WifiSpeaker-XXXXXX")
        print("   2. 设备IP地址正确 (************)")
        return
    
    # 执行所有测试
    tests = [
        ("设备信息", test_device_info),
        ("WiFi扫描", test_wifi_scan),
        ("WiFi状态", test_wifi_status),
        ("WiFi配置", test_wifi_config),
        ("真实配置", test_real_wifi_config),
        ("系统重置", test_system_reset)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except KeyboardInterrupt:
            print(f"\n\n⏹️  用户中断测试")
            break
        except Exception as e:
            print(f"\n❌ 测试 '{test_name}' 发生异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print_separator("测试结果汇总")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    print(f"\n📊 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有API接口测试通过！")
    else:
        print("⚠️  部分测试失败，请检查设备状态")
    
    print("\n📚 更多信息请参考 API_Documentation.md")

if __name__ == "__main__":
    main()
