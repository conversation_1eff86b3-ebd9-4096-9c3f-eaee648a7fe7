# The following lines of boilerplate have to be in your project's CMakeLists
# in this exact order for cmake to work correctly
cmake_minimum_required(VERSION 3.16)

# 设置编译选项，必须在project()之前
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wno-error=pointer-sign -Wno-error=char-subscripts -Wno-error=incompatible-pointer-types -Wno-error=sizeof-pointer-memaccess")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-error=pointer-sign -Wno-error=char-subscripts -Wno-error=incompatible-pointer-types -Wno-error=sizeof-pointer-memaccess")

set(IOT_SOLUTION_PATH "D:/project/esp32/esp-idf/esp-iot-solution" CACHE PATH "Path to ESP-IOT-SOLUTION")

#include($ENV{ADF_PATH}/CMakeLists.txt)
set(EXTRA_COMPONENT_DIRS
    "$ENV{ADF_PATH}/components"
    "${IOT_SOLUTION_PATH}/components/usb"
)
# 只保留 UAC，排除 UVC
set(EXCLUDE_COMPONENTS "usb_device_uvc" "usb_device_uac" )

include($ENV{IDF_PATH}/tools/cmake/project.cmake)
project(cloudMusic4G)
