#include "string.h"
#include "stdbool.h"
#include "cJSON.h"
#include "esp_log.h"
#include "jsonAdvantage.h"

static const char *TAG = "JSON";

// 修改后的getJsonData函数
bool json_wrapper_init(char* data, JsonWrapper* wrapper) {
    memset(wrapper, 0, sizeof(JsonWrapper)); // 强制清零
    wrapper->json = cJSON_Parse((char*)data);
    if (!wrapper->json) {
        wrapper->last_error = JSON_PARSE_ERROR;
        //wrapper->error_msg = "JSON解析失败";
        return false;
    }
    wrapper->last_error = JSON_OK;
    return true;
}

// 新增深拷贝函数
bool json_wrapper_copy(JsonWrapper* dest, const JsonWrapper* src) {
    if (!src || !src->json || !dest) {  // 增加dest空指针检查
        if (dest) dest->last_error = JSON_PARSE_ERROR;
        return false;
    }
    
    // 增加防御性检查
    if (dest->json && dest->json != src->json) {  // 防止自拷贝时误删
        cJSON_Delete(dest->json);
    }
    dest->json = NULL;  // 立即置空防止悬垂指针
    
    // 执行深拷贝
    dest->json = cJSON_Duplicate(src->json, true);
    if (!dest->json) {
        dest->last_error = JSON_PARSE_ERROR;
        return false;
    }
    
    dest->last_error = src->last_error;
    return true;
}


// 带异常检查的访问函数
cJSON* get_json_value(JsonWrapper* wrapper, const char* key) {
    if(!wrapper || !wrapper->json) {
        wrapper->last_error = JSON_KEY_NOT_FOUND;
        //wrapper->error_msg = "Invalid JSON root";
        return NULL;
    }
    
    cJSON* item = cJSON_GetObjectItem(wrapper->json, key);
    if(!item) {
        wrapper->last_error = JSON_KEY_NOT_FOUND;
        //wrapper->error_msg = key;
    }
    return item;
}

// 安全获取子对象
bool json_get_object(JsonWrapper* parent, const char* key, JsonWrapper* child) {
    cJSON* item = get_json_value(parent, key);
    if(parent->last_error != JSON_OK || !cJSON_IsObject(item)) {
        ESP_LOGE(TAG, "JSON对象类型错误[%d] at key: %s", parent->last_error, key);
        return false;
    }
    
    child->json = item;
    child->last_error = JSON_OK;
    return true;
}

// 安全获取字符串值
const char* json_get_string(JsonWrapper* wrapper, const char* key, const char* def) {
    cJSON* item = get_json_value(wrapper, key);
    if(wrapper->last_error != JSON_OK || !cJSON_IsString(item)) {
        ESP_LOGE(TAG, "JSON error[%d] at key: %s", wrapper->last_error, key);
        return def;
    }
    return item->valuestring;
}

// 安全获取整数值
int json_get_int(JsonWrapper* wrapper, const char* key, int def) {
    cJSON* item = get_json_value(wrapper, key);
    if(wrapper->last_error != JSON_OK || !cJSON_IsNumber(item)) {
        ESP_LOGE(TAG, "JSON整型错误[%d] at key: %s", wrapper->last_error, key);
        return def;
    }
    return item->valueint;
}

int json_get_double(JsonWrapper* wrapper, const char* key, int def) {
    cJSON* item = get_json_value(wrapper, key);
    if(wrapper->last_error != JSON_OK || !cJSON_IsNumber(item)) {
        ESP_LOGE(TAG, "JSON整型错误[%d] at key: %s", wrapper->last_error, key);
        return def;
    }
    return item->valuedouble; 
}

// 资源释放函数
void json_wrapper_free(JsonWrapper* wrapper) {
    if(wrapper) {
        if(wrapper->json) {
            cJSON_Delete(wrapper->json);
            wrapper->json = NULL;
        }
        // 始终重置错误状态
        wrapper->last_error = JSON_OK;
        //wrapper->error_msg = "";
    }
}



// 安全获取数组指定索引字符串
const char* json_get_specIndex_string(JsonWrapper* wrapper, const char* key, int index, const char* def) {
    cJSON* array = get_json_value(wrapper, key);
    if(wrapper->last_error != JSON_OK || !cJSON_IsArray(array)) {
        ESP_LOGE(TAG, "JSON数组错误[%d] at key: %s", wrapper->last_error, key);
        return def;
    }
    cJSON* item = cJSON_GetArrayItem(array, index);
    if(!item || !cJSON_IsString(item)) {
        ESP_LOGE(TAG, "JSON索引错误[%d] at index: %d", wrapper->last_error, index);
        return def;
    }
    return item->valuestring;
}

// 安全获取数组指定索引整型
int json_get_specIndex_int(JsonWrapper* wrapper, const char* key, int index, int def) {
    cJSON* array = get_json_value(wrapper, key);
    if(wrapper->last_error != JSON_OK || !cJSON_IsArray(array)) {
        ESP_LOGE(TAG, "JSON数组错误[%d] at key: %s", wrapper->last_error, key);
        return def;
    }
    cJSON* item = cJSON_GetArrayItem(array, index);
    if(!item || !cJSON_IsNumber(item)) {
        ESP_LOGE(TAG, "JSON索引错误[%d] at index: %d", wrapper->last_error, index);
        return def;
    }
    return item->valueint;
}

// 安全获取数组指定索引浮点型
double json_get_specIndex_double(JsonWrapper* wrapper, const char* key, int index, double def) {
    cJSON* array = get_json_value(wrapper, key);
    if(wrapper->last_error != JSON_OK || !cJSON_IsArray(array)) {
        ESP_LOGE(TAG, "JSON数组错误[%d] at key: %s", wrapper->last_error, key);
        return def;
    }
    cJSON* item = cJSON_GetArrayItem(array, index);
    if(!item || !cJSON_IsNumber(item)) {
        ESP_LOGE(TAG, "JSON索引错误[%d] at index: %d", wrapper->last_error, index);
        return def;
    }
    return item->valuedouble;
}