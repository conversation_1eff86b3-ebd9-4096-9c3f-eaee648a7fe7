/* Esptouch example

   This example code is in the Public Domain (or CC0 licensed, at your option.)

   Unless required by applicable law or agreed to in writing, this
   software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
   CONDITIONS OF ANY KIND, either express or implied.
*/

#include <string.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_wifi.h"
#include "esp_eap_client.h"
#include "esp_event.h"
#include "esp_log.h"
#include "esp_system.h"
#include "nvs_flash.h"
#include "esp_netif.h"
#include "esp_smartconfig.h"
#include "esp_mac.h"
#include "nvs.h"
#include "driver/gpio.h"
#include "esp_wifi_types.h"
#include "audio.h"
#include "uart.h"

#include "sysconf.h"
#include "softap_config.h"

#define GPIO_RESET_KEY_PIN     9
#define KEY_LONG_PRESS_MS   5000

#define GPIO_AMP_MUTE_PIN       10
#define GPIO_STATUS_LED_PIN    1

#define NVS_NAMESPACE_WIFI  "wifi_config"
#define SSID_KEY       "ssid"
#define PASSWORD_KEY   "password"

/* FreeRTOS event group to signal when we are connected & ready to make a request */
static EventGroupHandle_t s_wifi_event_group;

/* The event group allows multiple bits for each event,
   but we only care about one event - are we connected
   to the AP with an IP? */
static const int CONNECTED_BIT = BIT0;
static const char *TAG = "wifiSpeaker";

wifi_config_t wifi_config = {0};

static void softap_config_task(void * parm);
bool s_wifi_connected = false;

// 在全局变量区域新增
static bool s_has_wifi_config = false;
static char s_pending_ssid[33] = {0};      // 待保存的SSID
static char s_pending_password[65] = {0};  // 待保存的密码
static bool s_wifi_config_pending = false; // 是否有待保存的配置

static led_mode_t current_led_mode = LED_OFF;

static void wifi_monitor_task(void *arg);
static void initialise_wifi(void);
static void gpio_init();
static void start_network();
static void save_wifi_config_to_nvs(void);
void set_pending_wifi_config(const char* ssid, const char* password);


void start_mp3Player_url_task(char *url);

#if IS_RELEASE_VERSION
#include "driver/usb_serial_jtag.h"
bool EnableUsbGpio = true;
#else
bool EnableUsbGpio = false;
#endif

//GPIO设置功放有效
void GpioSetAmpValid(bool isValid)
{
    gpio_set_level(GPIO_AMP_MUTE_PIN, !isValid);
}

//GPIO输出网络连接状态(音乐信号闪烁)
void GPIO_OutPut_LedStatus(bool isValid)
{
    gpio_set_level(GPIO_STATUS_LED_PIN, isValid);
}

// 初始化GPIO
void system_gpio_init()
{
    // 功放GPIO
    gpio_config_t gpio_amp_mute_conf = {
        .pin_bit_mask = (1ULL << GPIO_AMP_MUTE_PIN),
        .mode = GPIO_MODE_OUTPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .intr_type = GPIO_INTR_DISABLE,
    };
    gpio_config(&gpio_amp_mute_conf);

    // LED指示灯GPIO
    gpio_config_t gpio_status_led_conf = {
        .pin_bit_mask = (1ULL << GPIO_STATUS_LED_PIN),
        .mode = GPIO_MODE_OUTPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .intr_type = GPIO_INTR_DISABLE,
    };

    gpio_config(&gpio_status_led_conf);

    // 初始化功放状态
    GpioSetAmpValid(false);
    // 初始化LED状态
    GPIO_OutPut_LedStatus(false);
}


void changeLedMode(led_mode_t mode)
{
    current_led_mode = mode;
    //如果模式为LED_ON或者PLAY_BLINK,但wifi未连接，那么灯的模式为SLOW_BLINK
    if(mode == LED_ON || mode == PLAY_BLINK)
    {
        if(s_wifi_connected == false)
        {
            current_led_mode = SLOW_BLINK;
        }
    }
}

static void event_handler(void* arg, esp_event_base_t event_base,
                                int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        #if 0
        if(!s_has_wifi_config)
        {
            // 启动SoftAP配网模式
            ESP_LOGI(TAG, "Starting SoftAP configuration mode");
            softap_start();
            http_server_start();
        }
        #endif
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        #if 0
        if (s_retry_num < 5) {
            esp_wifi_connect();
            s_retry_num++;
            ESP_LOGI(TAG, "Retry connecting to AP");
        } else {
            xEventGroupClearBits(s_wifi_event_group, CONNECTED_BIT);
        }
        #endif
        wifi_event_sta_disconnected_t *disconn = (wifi_event_sta_disconnected_t *)event_data;
        ESP_LOGE(TAG, "Disconnect reason: %d", disconn->reason); // 输出错误码
        if(s_wifi_connected) {
            s_wifi_connected = false;
            SendUart_CMD_Online();
        }

        // 更新WiFi配置状态
        extern wifi_config_status_t g_wifi_config_status;
        g_wifi_config_status = WIFI_CONFIG_FAILED;
        //xEventGroupClearBits(s_wifi_event_group, CONNECTED_BIT);
        if(!s_has_wifi_config)
        {
            ESP_LOGE(TAG, "WiFi connection failed, keeping SoftAP mode active");
        }
        else {
            changeLedMode(SLOW_BLINK); // 根据配置存在状态设置闪烁
            esp_wifi_connect();
        }
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_CONNECTED) {
        ESP_LOGI(TAG, "STA connected");
        // 更新WiFi配置状态
        extern wifi_config_status_t g_wifi_config_status;
        g_wifi_config_status = WIFI_CONFIG_CONNECTED;

        /* 20250623 获取到IP再关闭softAP模式
        // WiFi连接成功后，关闭SoftAP模式
        if (g_softap_started) {
            ESP_LOGI(TAG, "WiFi connected, shutting down SoftAP mode");
            softap_shutdown_on_wifi_connected();
        }
        */
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        xEventGroupSetBits(s_wifi_event_group, CONNECTED_BIT);
        if(!s_wifi_connected) {
            s_wifi_connected = true;
            SendUart_CMD_Online();
        }
        changeLedMode(LED_ON);  // 连接成功常亮
        ESP_LOGI(TAG, "IP obtained, WiFi marked as connected");

        // 更新WiFi配置状态
        extern wifi_config_status_t g_wifi_config_status;
        g_wifi_config_status = WIFI_CONFIG_CONNECTED;

        // 获取IP后保存WiFi配置到NVS
        save_wifi_config_to_nvs();

        // 获取IP后确保SoftAP模式已关闭
        if (g_softap_started) {
            ESP_LOGI(TAG, "IP obtained, ensuring SoftAP mode is stopped");
            softap_shutdown_on_wifi_connected();
        }
    }
}

static void initialise_wifi(void)
{
    // 初始化SoftAP配网系统
    ESP_ERROR_CHECK(softap_init());

    s_wifi_event_group = xEventGroupCreate();

    ESP_ERROR_CHECK( esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID, &event_handler, NULL) );
    ESP_ERROR_CHECK( esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, &event_handler, NULL) );

    ESP_ERROR_CHECK( esp_wifi_set_mode(WIFI_MODE_STA) );
    ESP_ERROR_CHECK( esp_wifi_start() );
}



// SoftAP配网任务（如果需要的话）
static void softap_config_task(void * parm)
{
    ESP_LOGI(TAG, "SoftAP configuration task started");

    // 启动SoftAP和HTTP服务器
    if (softap_start() == ESP_OK) {
        ESP_LOGI(TAG, "SoftAP started successfully");

        if (http_server_start() == ESP_OK) {
            ESP_LOGI(TAG, "HTTP server started successfully");
        }
    }

    // 等待WiFi连接成功
    EventBits_t uxBits;
    while (1) {
        uxBits = xEventGroupWaitBits(s_wifi_event_group, CONNECTED_BIT, false, false, 1000 / portTICK_PERIOD_MS);
        if(uxBits & CONNECTED_BIT) {
            ESP_LOGI(TAG, "WiFi Connected, SoftAP config task will exit");
            break;
        }
    }

    vTaskDelete(NULL);
}

// 新增定时检查任务
static void wifi_monitor_task(void *arg) {
    bool isWifiFirstConnected = false;
    uint32_t last_wifi_info_output_time = 0;
    while (1) {
        if (s_wifi_connected) {
            if(!isWifiFirstConnected || (xTaskGetTickCount() * portTICK_PERIOD_MS - last_wifi_info_output_time > 3000)) {
                last_wifi_info_output_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
                wifi_ap_record_t ap_info;
                if (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK) {
                    ESP_LOGI(TAG, "RSSI: %ddBm, SSID: %s", ap_info.rssi, ap_info.ssid);
                }
            }
            // 检查是否是第一次连接
            if (!isWifiFirstConnected) {
                isWifiFirstConnected = true;

                start_network();
            }
        } else {
            //ESP_LOGW(TAG, "Wifi disconnected");
        }
        #if 0
        size_t free_heap_size = esp_get_free_heap_size();
        ESP_LOGI("TAG", "Free heap size: %d bytes", free_heap_size);
        #endif
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
}

// 复位按钮、LED响应控制任务
static void button_led_task(void *arg) {
    // 按钮检测部分
    uint32_t press_start = 0;
    // LED控制部分
    bool led_state = false;
    uint32_t last_led_change = 0;

#if 0
    uint32_t last_usb_status_change = 0;
    bool usb_gpio = false;
#endif
    while (1) {
        #if 0
        if(EnableUsbGpio)
        {
            if (xTaskGetTickCount() * portTICK_PERIOD_MS - last_usb_status_change >= 1000) {
                usb_gpio =!usb_gpio;
                gpio_set_level(GPIO_NUM_18, usb_gpio);
                gpio_set_level(GPIO_NUM_19, usb_gpio);
                last_usb_status_change = xTaskGetTickCount() * portTICK_PERIOD_MS;
            }
        }
        #endif


        // 按钮检测逻辑
        if (gpio_get_level(GPIO_RESET_KEY_PIN) == 0) {
            if (press_start == 0) {
                press_start = xTaskGetTickCount() * portTICK_PERIOD_MS;
            } else if ((xTaskGetTickCount() * portTICK_PERIOD_MS - press_start) >= KEY_LONG_PRESS_MS) {
                // 长按处理
                nvs_handle_t handle;
                nvs_open(NVS_NAMESPACE_WIFI, NVS_READWRITE, &handle);
                nvs_erase_all(handle);
                nvs_commit(handle);
                nvs_close(handle);
                esp_restart();
            }
        } else {
            press_start = 0;
        }

        // LED控制逻辑
        switch(current_led_mode) {
            case LED_ON:
                gpio_set_level(GPIO_STATUS_LED_PIN, 1);
                break;
            case LED_OFF:
                gpio_set_level(GPIO_STATUS_LED_PIN, 0);
                break;
            case SLOW_BLINK:
                if (xTaskGetTickCount() * portTICK_PERIOD_MS - last_led_change > 1000) {
                    led_state = !led_state;
                    gpio_set_level(GPIO_STATUS_LED_PIN, led_state);
                    last_led_change = xTaskGetTickCount() * portTICK_PERIOD_MS;
                }
                break;
            case FAST_BLINK:
                if (xTaskGetTickCount() * portTICK_PERIOD_MS - last_led_change > 150) {
                    led_state = !led_state;
                    gpio_set_level(GPIO_STATUS_LED_PIN, led_state);
                    last_led_change = xTaskGetTickCount() * portTICK_PERIOD_MS;
                }
                break;
            case PLAY_BLINK:
                if (xTaskGetTickCount() * portTICK_PERIOD_MS - last_led_change > 300) {
                    led_state = !led_state;
                    gpio_set_level(GPIO_STATUS_LED_PIN, led_state);
                    last_led_change = xTaskGetTickCount() * portTICK_PERIOD_MS;
                }
                break;
        }

        vTaskDelay(50 / portTICK_PERIOD_MS); // 统一检测周期
    }
}


void system_init(void) {

    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    network_init();

    read_basic_config();

    start_network_client_task();
    while(1)
    {
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
}



static void start_network()
{
    //start_mp3Player_url_task("http://192.168.3.65:9999/Data/Program/Other/1KHz-0dB_60s_new.mp3");
    start_network_client_task();
}

// 设置待保存的WiFi配置
void set_pending_wifi_config(const char* ssid, const char* password)
{
    if (!ssid) {
        s_wifi_config_pending = false;
        return;
    }

    strncpy(s_pending_ssid, ssid, sizeof(s_pending_ssid) - 1);
    s_pending_ssid[sizeof(s_pending_ssid) - 1] = '\0';

    if (password) {
        strncpy(s_pending_password, password, sizeof(s_pending_password) - 1);
        s_pending_password[sizeof(s_pending_password) - 1] = '\0';
    } else {
        s_pending_password[0] = '\0';
    }

    s_wifi_config_pending = true;
    ESP_LOGI(TAG, "WiFi config set as pending: SSID=%s", s_pending_ssid);
}

// 保存WiFi配置到NVS
static void save_wifi_config_to_nvs(void)
{
    if (!s_wifi_config_pending) {
        ESP_LOGW(TAG, "No pending WiFi config to save");
        return;
    }

    nvs_handle_t handle;
    esp_err_t err = nvs_open(NVS_NAMESPACE_WIFI, NVS_READWRITE, &handle);
    if (err == ESP_OK) {
        ESP_LOGI(TAG, "Saving WiFi config to NVS after successful connection");

        if (nvs_set_blob(handle, SSID_KEY, s_pending_ssid, strlen(s_pending_ssid) + 1) != ESP_OK) {
            ESP_LOGE(TAG, "Failed to save SSID to NVS");
        }

        if (nvs_set_blob(handle, PASSWORD_KEY, s_pending_password, strlen(s_pending_password) + 1) != ESP_OK) {
            ESP_LOGE(TAG, "Failed to save password to NVS");
        }

        nvs_commit(handle);
        nvs_close(handle);

        ESP_LOGI(TAG, "WiFi config successfully saved to NVS: SSID=%s", s_pending_ssid);

        // 清除待保存状态
        s_wifi_config_pending = false;
        s_has_wifi_config = true;
    } else {
        ESP_LOGE(TAG, "Failed to open NVS for saving WiFi config: %s", esp_err_to_name(err));
    }
}