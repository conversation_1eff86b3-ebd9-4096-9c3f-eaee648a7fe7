# 新SD卡设置和格式化指南

## 问题描述
新的SD卡通常需要格式化才能在ESP32上正常使用。即使挂载成功，也可能出现"Invalid argument"错误，导致无法创建文件。

## 修复方案

### 1. 自动格式化支持
修改后的代码现在支持：
- 初始挂载时自动格式化（`format_if_mount_failed = true`）
- 检测到只读文件系统时手动格式化
- 挂载失败时强制手动格式化

### 2. 格式化流程
1. **初始挂载尝试**：使用`format_if_mount_failed = true`
2. **挂载失败处理**：如果挂载失败，执行手动格式化
3. **权限检查**：挂载成功后检查文件系统是否可写
4. **二次格式化**：如果检测到只读，再次格式化

### 3. 格式化参数
- 使用FatFS的`f_mkfs`函数
- 默认参数自动选择最佳格式（FAT16/FAT32）
- 工作缓冲区大小：`FF_MAX_SS`（通常是512字节）

## 使用方法

### 对于新SD卡
1. 插入新的SD卡
2. 运行程序，代码会自动检测并格式化
3. 查看日志输出确认格式化成功

### 预期日志输出
```
I (799) song_cache: Initializing SD card cache system...
I (809) song_cache: SD card mounted successfully at /sdcard
I (819) song_cache: Creating cache directory: /sdcard/cache
I (829) song_cache: Cache directory created successfully
I (839) song_cache: Testing file creation at: /sdcard/cache/test_permissions.tmp
I (849) song_cache: Cache directory ready and writable: /sdcard/cache
```

### 如果需要格式化的日志输出
```
W (799) song_cache: Failed to mount filesystem, attempting manual format...
I (809) song_cache: Performing manual SD card format...
I (1819) song_cache: Manual format successful, attempting remount...
I (1829) song_cache: SD card mounted successfully at /sdcard
```

## 故障排除

### 1. 如果格式化失败
- 检查SD卡是否有物理写保护开关
- 尝试使用不同的SD卡
- 检查SD卡是否损坏

### 2. 如果仍然无法创建文件
- 查看详细的错误日志
- 检查SD卡容量（建议使用32GB以下的卡）
- 确认SD卡类型（建议使用Class 10或更高）

### 3. 硬件检查
- 确认SD卡插入正确
- 检查SD卡座的连接
- 验证GPIO引脚配置

## 支持的SD卡类型
- SDSC (Standard Capacity): 最大2GB
- SDHC (High Capacity): 2GB-32GB
- SDXC (Extended Capacity): 32GB以上（可能需要额外配置）

## 推荐的SD卡规格
- 容量：8GB-32GB
- 速度等级：Class 10或更高
- 品牌：SanDisk、Kingston、Samsung等知名品牌
- 格式：建议使用全新的卡，让程序自动格式化

## 注意事项
1. 格式化会删除SD卡上的所有数据
2. 格式化过程可能需要几秒到几分钟
3. 不要在格式化过程中断电或移除SD卡
4. 建议在格式化前备份重要数据

## 代码修改总结
- 启用自动格式化选项
- 添加手动格式化逻辑
- 改进错误处理和日志输出
- 添加文件系统权限检查
- 创建专用缓存目录而不是使用根目录
