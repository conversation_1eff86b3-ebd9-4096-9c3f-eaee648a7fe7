#ifndef _NETWORK_H
#define _NETWORK_H

#include "const.h"

#define HTTP_SERVER_BASIC_ADDR    "http://api.iot.dmp.longzhiyin.cn:62080"
#define mqtt_pub_topic_name      "/trumpet1/trumpetA/upload"

#pragma pack(1)
typedef struct controlTcpHeader {
  unsigned int magic : 32;
  unsigned version : 8;
  unsigned serMethod : 8;
  unsigned messageType : 8;
  unsigned int sequenceId : 32;
  unsigned reserve : 8;
  unsigned int totalLen : 32;
  unsigned int jsonLen : 32;
} controlTcpHeader;


typedef enum  MessageType {
    LoginRequestMessage = 0, 
    LoginResponseMessage = 1,
    PingMessage = 14,
    PongMessage = 15,
    ControlSendInstructRequestMessage = 18,
    ControlSendInstructResponseMessage = 19,
    FileTransferMessage = 20,
    ClientSendProgressRequestMessage = 21,
    ClientSendProgressResponseMessage = 22,
    ServerSendInstructMessage = 23,
    ErrorResponseMessage = 24,
    TimeRequestMessage = 25,
    TimeResponseMessage = 26,
    ControlPlayTaskRequestMessage = 27,
    PlayTaskResponseMessage = 28,
    ServerSendTaskRequestMessage = 29,
    ControlUpdatePartitionRequestMessage = 30,
    ControlUpdatePartitionResponseMessage = 31,
    MiniAppsPlayTaskRequestMessage = 32
} MessageType;


// 控制命令
typedef enum
{
    CMD_SOUNDCARD_SERVER_EVENT         = 0x2000,       //声卡采集客户端事件
    CMD_SOUNDCARD_SERVER_STREAM        = 0x2001,        //声卡采集客户端采集
    CMD_SOUNDCARD_CLIENT_ONLINE        = 0x2002,       //声卡采集客户端上线
    CMD_SOUNDCARD_CLIENT_HEARTBEAT     = 0x2003,       //声卡采集客户端心跳
    CMD_SOUNDCARD_CLIENT_REQUEST_START_AGAIN   = 0x2004,  //声卡采集客户端请求重新开始采集
}ContrlCommand;

// 3.1设备型号
typedef enum
{
    MODEL_WIFI_SOUNDCARD_SERVER = 0x60,     // WIFI声卡服务器
    MODEL_WIFI_SOUNDCARD_DEVICE = 0x61,     // WIFI声卡设备
}DeviceModel;


extern int tcp_socket;
extern int soundCard_tcp_socket;
void network_init();
void start_network_client_task();
void soundCard_send_request_start_again();
#endif