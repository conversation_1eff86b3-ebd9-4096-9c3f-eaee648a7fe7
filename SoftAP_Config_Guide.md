# WiFi Speaker SoftAP 配网使用指南

## 概述

本项目已将原有的 airkiss 配网方式改为 SoftAP 配网方式，提供更稳定和用户友好的WiFi配置体验。

## 配网流程

### 1. 设备启动
- 设备启动后，如果没有保存的WiFi配置，会自动进入SoftAP配网模式
- LED指示灯会快速闪烁，表示进入配网模式
- 设备会创建一个名为 `WifiSpeaker-XXXXXX` 的WiFi热点（XXXXXX为设备码后6位）

### 2. SoftAP参数
- **SSID**: `WifiSpeaker-` + 设备码后6位
- **密码**: `wifi87654321`
- **IP地址**: `************`
- **端口**: `80`

### 3. 智能配网界面
1. 使用手机或电脑连接到设备的WiFi热点
2. 打开浏览器访问 `http://************`
3. 享受全新的智能配网体验：
   - **实时状态反馈**: 显示连接进度和状态变化
   - **智能进度条**: 可视化连接过程
   - **自动状态轮询**: 无需手动刷新页面
   - **错误处理**: 清晰的错误提示和重试机制
   - **响应式设计**: 适配各种设备屏幕
   - **用户友好**: 现代化的界面设计

## API接口

### 基础API

#### 1. 获取设备信息
```
GET /api/device/info
```
返回设备基本信息，包括设备码、版本信息、MAC地址等。

#### 2. WiFi配置
```
POST /api/wifi/config
Content-Type: application/json

{
    "ssid": "your_wifi_ssid",
    "password": "your_wifi_password"
}
```

#### 3. WiFi扫描
```
GET /api/wifi/scan
```
扫描并返回附近的WiFi网络列表。

#### 4. WiFi状态查询
```
GET /api/wifi/status
```
查询当前WiFi连接状态。





## 配网成功后

1. **智能WiFi配置保存**：
   - WiFi配置不会立即保存到NVS
   - 只有在获取到IP地址后才保存配置
   - 确保WiFi连接真正成功后才持久化配置
   - 避免保存无效的WiFi配置

2. **自动关闭SoftAP模式**：
   - 设备成功连接到WiFi后，会自动关闭SoftAP模式
   - HTTP服务器会优雅停止，确保最后的响应发送完成
   - WiFi模式从APSTA切换为STA模式
   - 释放SoftAP相关资源

2. **状态指示**：
   - LED指示灯变为常亮，表示WiFi连接成功
   - 设备热点消失，无法再连接

3. **网络服务**：
   - 设备开始正常的网络服务（TCP、MQTT连接等）
   - 可通过路由器分配的IP地址访问设备

## 重置配网

长按设备上的复位按钮5秒以上，设备会清除保存的WiFi配置并重启，重新进入配网模式。

## 技术特点

1. **稳定性**: 相比airkiss，SoftAP配网成功率更高
2. **兼容性**: 支持各种设备和操作系统
3. **扩展性**: 提供丰富的API接口，支持微信小程序集成
4. **安全性**: 支持WPA2加密的WiFi网络
5. **用户友好**: 提供直观的网页配置界面

## 故障排除

### 1. 无法找到设备热点
- 确认设备已进入配网模式（LED快速闪烁）
- 检查设备是否正常启动
- 尝试重启设备

### 2. 连接热点后无法访问配置页面
- 确认连接的是正确的设备热点
- 尝试访问 `http://************`
- 检查手机是否自动切换到移动数据

### 3. HTTP请求头过长错误
如果遇到 "431 Request Header Fields Too Large" 错误：
- 这通常是由于浏览器发送的请求头过长导致
- 已优化HTTP服务器配置，增加了缓冲区大小
- 建议使用简洁的浏览器或清除浏览器缓存

### 4. WiFi配置失败
- 确认WiFi密码正确
- 检查WiFi信号强度
- 确认WiFi网络支持2.4GHz频段

### 5. 配置成功但无法连接
- 检查路由器是否限制设备连接
- 确认WiFi网络正常工作
- 尝试重启路由器

### 6. favicon.ico 404错误
- 这是正常现象，不影响功能
- 已添加favicon处理器避免错误日志

## 开发说明

### 新增文件
- `main/softap_config.h` - SoftAP配网头文件
- `main/softap_config.c` - SoftAP配网实现
- `main/simple_page.h` - 智能配网页面头文件
- `main/simple_page.c` - 智能配网页面实现（包含JavaScript）

### 修改文件
- `main/main.c` - 主程序，集成SoftAP配网，统一事件处理
- `main/CMakeLists.txt` - 添加新源文件

### 配置要求
- ESP-IDF 4.4+
- 支持HTTP服务器组件
- 支持cJSON库

### HTTP服务器优化
为了解决"431 Request Header Fields Too Large"错误，已进行以下优化：

1. **sdkconfig配置优化**：
   - `CONFIG_HTTPD_MAX_REQ_HDR_LEN=4096` (原512)
   - `CONFIG_HTTPD_MAX_URI_LEN=2048` (原512)
   - `CONFIG_HTTPD_PURGE_BUF_LEN=128` (原32)

2. **HTTP服务器配置优化**：
   - 增加URI处理器数量到20个
   - 增加栈大小到16KB
   - 增加响应头数量到16个
   - 优化超时设置

3. **代码优化**：
   - 简化HTML页面，减少请求复杂度
   - 优化API注册，支持部分注册失败
   - 改进内存管理和错误处理
   - 统一事件处理器，避免重复代码
