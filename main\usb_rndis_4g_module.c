/*
 * SPDX-FileCopyrightText: 2024-2025 Espressif Systems (Shanghai) CO LTD
*
* SPDX-License-Identifier: Apache-2.0
*/
#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_task_wdt.h"
#include "esp_err.h"
#include "esp_log.h"
#include "esp_check.h"
#include "esp_event.h"
#include "esp_netif.h"
#include "esp_event.h"
#include "dhcpserver/dhcpserver_options.h"
#include "ping/ping_sock.h"
#include "iot_usbh_rndis.h"
#include "nvs_flash.h"
//#include "app_wifi.h"
#include "iot_eth.h"
#include "iot_eth_netif_glue.h"
#include "iot_usbh_cdc.h"
#include "usb/usb_host.h"
#include "usb/usb_types_ch9.h"

#include "driver/gpio.h"
#include "rom/gpio.h"
#include "usb_rndis_4g_module.h"

#include "sysconf.h"

static const char *TAG = "RNDIS_4G_MODULE";


// AT命令宏定义
#define AT_CMD_CREG "AT+CEREG?\r\n"
#define AT_CMD_CSQ "AT+CSQ\r\n"
#define AT_CMD_CPIN "AT+CPIN?\r\n"
#define AT_CMD_CCID "AT+MCCID\r\n"
#define AT_CMD_AUTODIAL_QUERY "AT+MDIALUPCFG=\"auto\"\r\n"
#define AT_CMD_AUTODIAL_ENABLE "AT+MDIALUPCFG=\"auto\",1\r\n"

// AT通讯相关全局变量
static usbh_cdc_handle_t at_cdc_handle = NULL;
static bool at_comm_initialized = false;
static TaskHandle_t at_task_handle = NULL;
static bool at_device_connected = false;  // 跟踪设备连接状态
static int consecutive_failures = 0;  // 连续失败计数器
static bool eth_connected = false;  // 以太网连接状态
_stModule4GInfo g_4g_info = {0};  // 4G模块信息

#define MAX_ETH_FAILURES    15
#define MAX_AT_CONSECUTIVE_FAILURES  60

// AT通讯配置
#define CONFIG_MODEM_USB_OUT_EP_ADDR 0x0A
#define CONFIG_MODEM_USB_IN_EP_ADDR  0x81
#define MODEM_USB_ITF_NUM            2
#define AT_BUFFER_SIZE               2048

// AT通讯函数声明
static void at_communication_task(void *param);
static esp_err_t send_at_command(const char *cmd, char *response, size_t response_size, uint32_t timeout_ms);
static void at_connect_callback(usbh_cdc_handle_t cdc_handle, void *user_data);
static void at_disconnect_callback(usbh_cdc_handle_t cdc_handle, void *user_data);
static bool parse_cereg_response(const char *response, unsigned char *creg_status);
static bool parse_csq_response(const char *response, unsigned char *csq_rssi);
static bool parse_cpin_response(const char *response);
static bool parse_ccid_response(const char *response, char *iccid);
static bool parse_autodial_response(const char *response, bool *autodial_enabled);


//4G模块GPIO引脚定义
#define GPIO_NUM_4G_POWEREN   GPIO_NUM_13
#define GPIO_NUM_4G_POWERKEY   GPIO_NUM_11
#define GPIO_NUM_4G_RESET   GPIO_NUM_12
#define GPIO_NUM_4G_USB_SELECT  GPIO_NUM_21



//4G模块初始化
void Module_4G_Init()
{
    //初始化GPIO
    system_gpio_init();

    //初始化4G POWERKEY引脚
    gpio_pad_select_gpio(GPIO_NUM_4G_POWERKEY);
    gpio_set_direction(GPIO_NUM_4G_POWERKEY, GPIO_MODE_OUTPUT);
    gpio_set_level(GPIO_NUM_4G_POWERKEY, 1);
    vTaskDelay(pdMS_TO_TICKS(200));

    //初始化4G POWEREN引脚
    gpio_reset_pin(GPIO_NUM_4G_POWEREN);
    gpio_set_direction(GPIO_NUM_4G_POWEREN, GPIO_MODE_OUTPUT);
    gpio_set_level(GPIO_NUM_4G_POWEREN, 1);
}

void System_Reboot(bool bReset4G)
{
    //关闭4G电源，然后再重启
    printf("System_Reboot:bReset4G=%d\n", bReset4G);

    if(bReset4G)
    {
        gpio_set_level(GPIO_NUM_4G_POWEREN, 0);
        vTaskDelay(pdMS_TO_TICKS(4000));
    }
    else
    {
        vTaskDelay(pdMS_TO_TICKS(1500));
    }

    esp_restart();
}

static void on_ping_success(esp_ping_handle_t hdl, void *args)
{
    uint8_t ttl;
    uint16_t seqno;
    uint32_t elapsed_time, recv_len;
    ip_addr_t target_addr;
    esp_ping_get_profile(hdl, ESP_PING_PROF_SEQNO, &seqno, sizeof(seqno));
    esp_ping_get_profile(hdl, ESP_PING_PROF_TTL, &ttl, sizeof(ttl));
    esp_ping_get_profile(hdl, ESP_PING_PROF_IPADDR, &target_addr, sizeof(target_addr));
    esp_ping_get_profile(hdl, ESP_PING_PROF_SIZE, &recv_len, sizeof(recv_len));
    esp_ping_get_profile(hdl, ESP_PING_PROF_TIMEGAP, &elapsed_time, sizeof(elapsed_time));
    ESP_LOGI(TAG, "%"PRIu32" bytes from %s icmp_seq=%u ttl=%u time=%"PRIu32" ms\n", recv_len, ipaddr_ntoa(&target_addr), seqno, ttl, elapsed_time);
}

static void on_ping_timeout(esp_ping_handle_t hdl, void *args)
{
    uint16_t seqno;
    ip_addr_t target_addr;
    esp_ping_get_profile(hdl, ESP_PING_PROF_SEQNO, &seqno, sizeof(seqno));
    esp_ping_get_profile(hdl, ESP_PING_PROF_IPADDR, &target_addr, sizeof(target_addr));
    ESP_LOGW(TAG, "From %s icmp_seq=%u timeout\n", ipaddr_ntoa(&target_addr), seqno);
    // Users can add logic to handle ping timeout
    // Add Wait or Reset logic
}

// AT通讯回调函数实现
static void at_connect_callback(usbh_cdc_handle_t cdc_handle, void *user_data)
{
    ESP_LOGI(TAG, "AT Communication Device Connected!");
    at_device_connected = true;
    consecutive_failures = 0;
}

static void at_disconnect_callback(usbh_cdc_handle_t cdc_handle, void *user_data)
{
    ESP_LOGW(TAG, "AT Communication Device Disconnected!");
    at_device_connected = false;
    at_comm_initialized = false;
    
    // 清理资源，避免访问已断开的设备
    if (at_cdc_handle == cdc_handle) {
        at_cdc_handle = NULL;
    }
    
    // 如果AT任务正在运行，让它知道设备已断开
    ESP_LOGW(TAG, "AT device disconnected, task will handle reconnection");
}

// AT命令发送函数（带重试机制）
static esp_err_t send_at_command(const char *cmd, char *response, size_t response_size, uint32_t timeout_ms)
{
    // 添加更严格的参数检查
    if (cmd == NULL) {
        ESP_LOGE(TAG, "AT command is NULL");
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!at_comm_initialized || at_cdc_handle == NULL) {
        ESP_LOGE(TAG, "AT communication not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    const int max_retries = 3;
    const int retry_delay_ms = 500;
    
    for (int retry = 0; retry < max_retries; retry++) {
        // 发送AT命令前再次检查设备状态
        size_t dummy_size = 0;
        esp_err_t status_check = usbh_cdc_get_rx_buffer_size(at_cdc_handle, &dummy_size);
        if (status_check != ESP_OK) {
            ESP_LOGW(TAG, "Device status check failed (retry %d/%d): %s", retry + 1, max_retries, esp_err_to_name(status_check));
            if (retry == max_retries - 1) {
                at_comm_initialized = false;
                return status_check;
            }
            vTaskDelay(pdMS_TO_TICKS(retry_delay_ms));
            continue;
        }

        // 发送AT命令
        size_t cmd_len = strlen(cmd);
        esp_err_t ret = usbh_cdc_write_bytes(at_cdc_handle, (uint8_t *)cmd, cmd_len, pdMS_TO_TICKS(timeout_ms));
        if (ret != ESP_OK) {
            ESP_LOGW(TAG, "Failed to send AT command (retry %d/%d): %s", retry + 1, max_retries, esp_err_to_name(ret));
            if (ret == ESP_ERR_INVALID_STATE) {
                at_comm_initialized = false;
                return ret;
            }
            if (retry < max_retries - 1) {
                vTaskDelay(pdMS_TO_TICKS(retry_delay_ms));
                continue;
            }
            return ret;
        }
#if DEBUG_AT_COMMAND
        ESP_LOGI(TAG, "Sent AT command: %s", cmd);
#endif
        // 接收响应
        if (response != NULL && response_size > 0) {
            size_t received_len = response_size - 1; // 保留一个字节给\0
            ret = usbh_cdc_read_bytes(at_cdc_handle, (uint8_t *)response, &received_len, pdMS_TO_TICKS(timeout_ms));
            if (ret == ESP_OK && received_len > 0) {
                response[received_len] = '\0'; // 确保字符串结束
                #if DEBUG_AT_COMMAND
                ESP_LOGI(TAG, "Received AT response (%d bytes): %s", received_len, response);
                #endif
                return ESP_OK; // 成功，退出重试循环
            } else if (ret == ESP_ERR_TIMEOUT) {
                ESP_LOGW(TAG, "AT response timeout (retry %d/%d)", retry + 1, max_retries);
                if (response_size > 0) {
                    response[0] = '\0';
                }
                if (retry < max_retries - 1) {
                    vTaskDelay(pdMS_TO_TICKS(retry_delay_ms));
                    continue;
                }
            } else {
                ESP_LOGW(TAG, "Failed to receive AT response (retry %d/%d): %s", retry + 1, max_retries, esp_err_to_name(ret));
                if (ret == ESP_ERR_INVALID_STATE) {
                    at_comm_initialized = false;
                    return ret;
                }
                if (response_size > 0) {
                    response[0] = '\0';
                }
                if (retry < max_retries - 1) {
                    vTaskDelay(pdMS_TO_TICKS(retry_delay_ms));
                    continue;
                }
                return ret;
            }
        } else {
            return ESP_OK; // 不需要读取响应，命令发送成功
        }
    }

    return ESP_ERR_TIMEOUT; // 所有重试都失败
}

// 解析CEREG响应，提取网络注册状态
static bool parse_cereg_response(const char *response, unsigned char *creg_status)
{
    if (response == NULL || creg_status == NULL) {
        return false;
    }
    
    // 查找 +CEREG: 响应
    const char *cereg_pos = strstr(response, "+CEREG:");
    if (cereg_pos == NULL) {
        return false;
    }
    
    // 解析格式: +CEREG: n,stat
    int n, stat;
    if (sscanf(cereg_pos, "+CEREG: %d,%d", &n, &stat) == 2) {
        *creg_status = (unsigned char)stat;
        ESP_LOGI(TAG, "Parsed CEREG status: %d", stat);
        return true;
    }
    
    return false;
}

// 解析CSQ响应，提取信号强度
static bool parse_csq_response(const char *response, unsigned char *csq_rssi)
{
    if (response == NULL || csq_rssi == NULL) {
        return false;
    }
    
    // 查找 +CSQ: 响应
    const char *csq_pos = strstr(response, "+CSQ:");
    if (csq_pos == NULL) {
        return false;
    }
    
    // 解析格式: +CSQ: rssi,ber
    int rssi, ber;
    if (sscanf(csq_pos, "+CSQ: %d,%d", &rssi, &ber) == 2) {
        *csq_rssi = (unsigned char)rssi;
        //ESP_LOGI(TAG, "Parsed CSQ RSSI: %d", rssi);
        return true;
    }
    
    return false;
}

// 解析CPIN响应，检查SIM卡状态
static bool parse_cpin_response(const char *response)
{
    if (response == NULL) {
        return false;
    }
    
    // 查找 +CPIN: 响应
    const char *cpin_pos = strstr(response, "+CPIN:");
    if (cpin_pos == NULL) {
        return false;
    }
    
    // 解析格式: +CPIN: READY 或其他状态
    if (strstr(cpin_pos, "READY") != NULL) {
        ESP_LOGI(TAG, "SIM card is ready");
        return true;
    }
    
    return false;
}

// 解析CCID响应，提取ICCID
static bool parse_ccid_response(const char *response, char *iccid)
{
    if (response == NULL || iccid == NULL) {
        return false;
    }
    
    // 查找 +MCCID: 响应
    const char *ccid_pos = strstr(response, "+MCCID:");
    if (ccid_pos == NULL) {
        return false;
    }
    
    // 跳过 "+MCCID: " 前缀
    ccid_pos += 8;
    
    // 跳过空格
    while (*ccid_pos == ' ') {
        ccid_pos++;
    }
    
    // 提取ICCID号码（20位数字）
    int i = 0;
    while (i < 20 && *ccid_pos >= '0' && *ccid_pos <= '9') {
        iccid[i] = *ccid_pos;
        i++;
        ccid_pos++;
    }
    
    if (i == 20) {
        iccid[20] = '\0';
        ESP_LOGI(TAG, "Parsed ICCID: %s", iccid);
        return true;
    }
    
    return false;
}

// 解析自动拨号响应
static bool parse_autodial_response(const char *response, bool *autodial_enabled)
{
    if (response == NULL || autodial_enabled == NULL) {
        return false;
    }
    
    // 查找 +MDIALUPCFG: 响应
    const char *autodial_pos = strstr(response, "+MDIALUPCFG:");
    if (autodial_pos == NULL) {
        return false;
    }
    
    // 解析格式: +MDIALUPCFG: "auto",1 或 +MDIALUPCFG: "auto",0
    if (strstr(autodial_pos, "\"auto\",1") != NULL) {
        *autodial_enabled = true;
        ESP_LOGI(TAG, "Auto dial is enabled");
        return true;
    } else if (strstr(autodial_pos, "\"auto\",0") != NULL) {
        *autodial_enabled = false;
        ESP_LOGI(TAG, "Auto dial is disabled");
        return true;
    }
    
    return false;
}

// AT通讯任务
// 静态分配响应缓冲区，避免栈溢出
static char at_response_buffer[AT_BUFFER_SIZE];

static void at_communication_task(void *param)
{
    size_t rx_buffer_size = 0;
    
    // 状态管理变量
    static uint32_t last_creg_check = 0;
    static uint32_t last_csq_check = 0;
    static uint32_t last_iccid_check = 0;
    static bool autodial_checked = false;
    static bool sim_ready_checked = false;
    
    ESP_LOGI(TAG, "AT communication task started");

    // 初始化4G模块信息
    memset(&g_4g_info, 0, sizeof(g_4g_info));

    //延时2秒，等待稳定
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    while (1) {
        uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
        
        // 检查AT通讯是否就绪
        if (at_comm_initialized && at_device_connected && at_cdc_handle != NULL) {
            // 先检查设备是否仍然有效
            esp_err_t status_ret = usbh_cdc_get_rx_buffer_size(at_cdc_handle, &rx_buffer_size);
            if (status_ret != ESP_OK) {
                ESP_LOGW(TAG, "USB device may be disconnected, status check failed: %s", esp_err_to_name(status_ret));
                consecutive_failures++;
                if (consecutive_failures >= MAX_AT_CONSECUTIVE_FAILURES) {
                    ESP_LOGE(TAG, "Too many consecutive failures (%d), reboot", consecutive_failures);
                    #if 0
                    at_device_connected = false;
                    at_comm_initialized = false;
                    consecutive_failures = 0;
                    // 重置状态标志
                    autodial_checked = false;
                    sim_ready_checked = false;
                    memset(&g_4g_info, 0, sizeof(g_4g_info));
                    vTaskDelay(pdMS_TO_TICKS(recovery_delay_ms));
                    #else
                    //复位系统
                    System_Reboot(true);
                    #endif
                } else {
                    vTaskDelay(pdMS_TO_TICKS(1000));
                }
                continue;
            }
            
            // 清空接收缓冲区
            if (rx_buffer_size > 0) {
                size_t read_len = rx_buffer_size;
                if (read_len > sizeof(at_response_buffer) - 1) {
                    read_len = sizeof(at_response_buffer) - 1;
                }
                esp_err_t ret = usbh_cdc_read_bytes(at_cdc_handle, (uint8_t *)at_response_buffer, &read_len, pdMS_TO_TICKS(100));
                if (ret == ESP_OK && read_len > 0) {
                    at_response_buffer[read_len] = '\0';
                    ESP_LOGD(TAG, "Cleared buffer data (%d bytes): %s", read_len, at_response_buffer);
                }
            }
            
            // 4G模块状态管理逻辑
            
            // 1. 检查网络注册状态（如果未注册，间隔1秒检查）
            if (g_4g_info.creg_status != 1) {
                if (current_time - last_creg_check >= 1000) {
                    esp_err_t ret = send_at_command(AT_CMD_CREG, at_response_buffer, sizeof(at_response_buffer), 2000);
                    if (ret == ESP_OK) {
                        unsigned char new_status;
                        if (parse_cereg_response(at_response_buffer, &new_status)) {
                            g_4g_info.creg_status = new_status;
                            ESP_LOGI(TAG, "Network registration status: %d", new_status);
                            if (new_status == 1) {
                                ESP_LOGI(TAG, "Network registered successfully!");
                                // 重置自动拨号检查标志
                                autodial_checked = false;
                            }
                        }
                    }
                    last_creg_check = current_time;
                }
            }
            
            // 2. 当网络注册成功后，检查自动拨号模式
            else if (g_4g_info.creg_status == 1 && !autodial_checked) {
                esp_err_t ret = send_at_command(AT_CMD_AUTODIAL_QUERY, at_response_buffer, sizeof(at_response_buffer), 2000);
                if (ret == ESP_OK) {
                    bool autodial_enabled;
                    if (parse_autodial_response(at_response_buffer, &autodial_enabled)) {
                        if (!autodial_enabled) {
                            ESP_LOGI(TAG, "Auto dial is disabled, enabling it...");
                            ret = send_at_command(AT_CMD_AUTODIAL_ENABLE, at_response_buffer, sizeof(at_response_buffer), 2000);
                            if (ret == ESP_OK && strstr(at_response_buffer, "OK") != NULL) {
                                ESP_LOGI(TAG, "Auto dial enabled successfully, restarting device...");
                                vTaskDelay(pdMS_TO_TICKS(1000));
                                System_Reboot(true);
                            }
                        } else {
                            ESP_LOGI(TAG, "Auto dial is already enabled");
                            autodial_checked = true;
                        }
                    }
                }
            }
            
            // 3. 当网络注册成功且自动拨号已检查后，获取ICCID（如果还未获取）
            else if (g_4g_info.creg_status == 1 && autodial_checked && strlen(g_4g_info.iccid) == 0) {
                if (current_time - last_iccid_check >= 1000) {
                    // 首先检查SIM卡状态
                    if (!sim_ready_checked) {
                        esp_err_t ret = send_at_command(AT_CMD_CPIN, at_response_buffer, sizeof(at_response_buffer), 2000);
                        if (ret == ESP_OK && parse_cpin_response(at_response_buffer)) {
                            sim_ready_checked = true;
                        }
                    } else {
                        // SIM卡就绪后获取ICCID
                        esp_err_t ret = send_at_command(AT_CMD_CCID, at_response_buffer, sizeof(at_response_buffer), 2000);
                        if (ret == ESP_OK) {
                            if (parse_ccid_response(at_response_buffer, g_4g_info.iccid)) {
                                ESP_LOGI(TAG, "ICCID obtained: %s", g_4g_info.iccid);
                                // 设置模块信息有效
                                g_4g_info.isValid = true;
                                ESP_LOGI(TAG, "4G module info is now valid!");
                            }
                        }
                    }
                    last_iccid_check = current_time;
                }
            }
            
            // 4. 当网络注册成功后，每10秒获取信号强度
            else if (g_4g_info.creg_status == 1) {
                if (current_time - last_csq_check >= 10000) {
                    esp_err_t ret = send_at_command(AT_CMD_CSQ, at_response_buffer, sizeof(at_response_buffer), 2000);
                    if (ret == ESP_OK) {
                        unsigned char new_rssi;
                        if (parse_csq_response(at_response_buffer, &new_rssi)) {
                            g_4g_info.csq_rssi = new_rssi;
                            ESP_LOGI(TAG, "Signal strength (RSSI): %d", new_rssi);
                        }
                    }
                    last_csq_check = current_time;
                }
            }
            
            // 重置失败计数
            consecutive_failures = 0;
            vTaskDelay(pdMS_TO_TICKS(100));
            
        } else {
            // AT通讯未就绪，尝试初始化
            if (at_device_connected) {
                at_cdc_handle = usbh_rndis_get_at_cdc_handle();
                if (at_cdc_handle == NULL) {
                    ESP_LOGW(TAG, "AT interface CDC handle not available yet, will retry later");
                } else {
                    at_comm_initialized = true;
                    // 重置状态标志
                    autodial_checked = false;
                    sim_ready_checked = false;
                    memset(&g_4g_info, 0, sizeof(g_4g_info));
                    ESP_LOGI(TAG, "AT communication initialized");
                }
            }
            vTaskDelay(pdMS_TO_TICKS(1000));
        }
    }
}

static void iot_event_handle(void *arg, esp_event_base_t event_base, int32_t event_id, void *event_data)
{
    switch (event_id) {
    case IOT_ETH_EVENT_START:
        ESP_LOGI(TAG, "IOT_ETH_EVENT_START");
        break;
    case IOT_ETH_EVENT_STOP:
        ESP_LOGI(TAG, "IOT_ETH_EVENT_STOP");
        break;
    case IOT_ETH_EVENT_CONNECTED:
        ESP_LOGI(TAG, "IOT_ETH_EVENT_CONNECTED - RNDIS connection established");
        eth_connected = true;
        
        // 创建AT通讯任务，任务内部会自动初始化AT通讯
        if (at_task_handle == NULL) {
            xTaskCreate(at_communication_task, "at_comm_task", 8192, NULL, 5, &at_task_handle);
            ESP_LOGI(TAG, "AT communication task created");
        }
        break;
    case IOT_ETH_EVENT_DISCONNECTED:
        ESP_LOGI(TAG, "IOT_ETH_EVENT_DISCONNECTED - RNDIS connection lost");
        eth_connected = false;
        // 网络断开时清理AT通讯资源
        at_comm_initialized = false;
        at_device_connected = false;
        at_cdc_handle = NULL;  // RNDIS模块会清理AT接口CDC句柄
        ESP_LOGI(TAG, "AT communication resources cleared");
        break;
    default:
        ESP_LOGI(TAG, "IOT_ETH_EVENT_UNKNOWN");
        break;
    }
}

esp_err_t song_cache_init(void);
void app_main(void)
{
    #if 0
    // 初始化任务看门狗，并启用系统复位功能
    /*
    0x00：不监控任何核心的空闲任务。
0x01：只监控 Core 0 的空闲任务。
0x02：只监控 Core 1 的空闲任务。
0x03：监控 Core 0 和 Core 1 的空闲任务。*/
    // 设置任务看门狗触发时复位系统
    //如果idle_core_mask  = 3，不用手动esp_task_wdt_add,会自动监控所有空闲任务
    esp_task_wdt_config_t wdt_config = {
        .timeout_ms = 10 * 1000,  // 超时时间，单位为毫秒
        .idle_core_mask = 0,    
        .trigger_panic = true,                 // 超时时触发系统复位
    };
    esp_task_wdt_init(&wdt_config);
    // 将 app_main 任务添加到任务看门狗的监控列表中
    ESP_ERROR_CHECK(esp_task_wdt_add(NULL));
#endif

    // 非发布版本，延时4秒，以便烧录程序
    // 发布版本，延时2秒，等待电平稳定
    #if 0
    #if !IS_RELEASE_VERSION
    vTaskDelay(pdMS_TO_TICKS(4000));
    #else
    vTaskDelay(pdMS_TO_TICKS(2000));
    #endif
    #endif

    song_cache_init();

    Module_4G_Init();

    #if IS_CLOSE_USB_UART_LOG
    esp_log_level_set("*", ESP_LOG_NONE);
    #else
    esp_log_level_set("*", ESP_LOG_INFO);
    #endif

    /* Initialize default TCP/IP stack */
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());


    esp_event_handler_register(IOT_ETH_EVENT, ESP_EVENT_ANY_ID, iot_event_handle, NULL);

    // install usbh cdc driver
    usbh_cdc_driver_config_t config = {
        .task_stack_size = 1024 * 4,
        .task_priority = 5,
        .task_coreid = 0,
        .skip_init_usb_host_driver = false,
    };
    ESP_ERROR_CHECK(usbh_cdc_driver_install(&config));

    iot_usbh_rndis_config_t rndis_cfg = {
        .auto_detect = true,
        .auto_detect_timeout = pdMS_TO_TICKS(1000),
    };

    // Configure AT interface CDC device
    usbh_cdc_device_config_t at_cdc_config = {
        .vid = 0x2ecc,  // 4G module VID
        .pid = 0x3012,  // 4G module PID
        .itf_num = 2,   // AT communication interface
        .rx_buffer_size = 1024,
        .tx_buffer_size = 1024,
        .cbs = {
            .connect = at_connect_callback,
            .disconnect = at_disconnect_callback,
            .recv_data = NULL,  // Will use polling mode
            .notif_cb = NULL,
            .user_data = NULL
        }
    };

    iot_eth_driver_t *rndis_handle = NULL;
    esp_err_t ret = iot_eth_new_usb_rndis(&rndis_cfg, &at_cdc_config, &rndis_handle);
    if (ret != ESP_OK || rndis_handle == NULL) {
        ESP_LOGE(TAG, "Failed to create USB RNDIS driver");
        return;
    }

    iot_eth_config_t eth_cfg = {
        .driver = rndis_handle,
        .stack_input = NULL,
        .user_data = NULL,
    };
        iot_eth_handle_t eth_handle = NULL;
    ret = iot_eth_install(&eth_cfg, &eth_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to install USB RNDIS driver");
        return;
    }

    esp_netif_config_t netif_cfg = ESP_NETIF_DEFAULT_ETH();
    esp_netif_t *eth_netif = esp_netif_new(&netif_cfg);

    iot_eth_netif_glue_handle_t glue = iot_eth_new_netif_glue(eth_handle);
    if (glue == NULL) {
        ESP_LOGE(TAG, "Failed to create netif glue");
        return;
    }
    esp_netif_attach(eth_netif, glue);

    // 记录创建时间，用于检测是否需要开机
    uint32_t eth_create_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    bool power_on_attempted = false;

    while (1) {
        uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
        if (!power_on_attempted && (current_time - eth_create_time) >= 10000) {
            ESP_LOGI(TAG, "4G module wait 10s completed");
            power_on_attempted = true;
        }
        if(power_on_attempted)
        {
            //如果连续15次以太网没有成功，则重启系统
            static unsigned char eth_failures = 0;
            const int max_eth_failures = 15;
            if (eth_failures >= max_eth_failures) {
                ESP_LOGE(TAG, "eth_failures %d >= %d, reboot", eth_failures, max_eth_failures);
                //复位系统
                System_Reboot(true);
            }
            ret = iot_eth_start(eth_handle);
            if (ret != ESP_OK) {
                ESP_LOGW(TAG, "Failed to start USB RNDIS driver, try again...");
                eth_failures++;  // 增加失败计数器，用于检测是否需要重启系统。
            }
            else
            {
                break;
            }
        }
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }

    unsigned short wait_Cnt=0;
    while (1) {
        if(g_4g_info.isValid)
        {
            break;
        }
        //如果超时5分钟4G模块还未生效，则重启系统
        if(++wait_Cnt>300) //5分钟
        {
            System_Reboot(true);
        }
        vTaskDelay(1000 / portTICK_PERIOD_MS);
    }
    //延时3秒，等待网络稳定
    vTaskDelay(3000 / portTICK_PERIOD_MS);

    system_init();
}
