#ifndef JSONADVANTAGE_H
#define JSONADVANTAGE_H

#include <stdbool.h>
#include "cJSON.h"


// 在错误码枚举中新增解析错误
typedef enum {
    JSON_OK,
    JSON_PARSE_ERROR,  // 新增解析错误码
    JSON_KEY_NOT_FOUND,
    JSON_TYPE_MISMATCH
} JsonErrorCode;

// 增强结构体封装
typedef struct {
    cJSON* json;
    JsonErrorCode last_error;
    //const char* error_msg;
} JsonWrapper;

// 初始化JsonWrapper
bool json_wrapper_init(char* data, JsonWrapper* wrapper);
bool json_wrapper_copy(JsonWrapper* dest, const JsonWrapper* src);
// 获取子对象
bool json_get_object(JsonWrapper* parent, const char* key, JsonWrapper* child);
// 释放JsonWrapper
void json_wrapper_free(JsonWrapper* wrapper);
// 获取json的string值，def代表如果出错，返回的默认值
const char* json_get_string(JsonWrapper* wrapper, const char* key, const char* def);
// 获取json的int值，def代表如果出错，返回的默认值
int json_get_int(JsonWrapper* wrapper, const char* key, int def);
// 获取json的double值，def代表如果出错，返回的默认值
int json_get_double(JsonWrapper* wrapper, const char* key, int def);
const char* json_get_specIndex_string(JsonWrapper*, const char*, int, const char*);
int json_get_specIndex_int(JsonWrapper*, const char*, int, int);
double json_get_specIndex_double(JsonWrapper*, const char*, int, double);

#endif