#ifndef SONG_CACHE_H
#define SONG_CACHE_H

#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"

// SD卡缓存配置
#define CACHE_MOUNT_POINT "/sdcard"
#define CACHE_DIR_PATH "/sdcard"
#define MIN_FREE_SPACE_MB 5   // 保留最小空闲空间5MB
#define MAX_FILENAME_LEN 64
#define MAX_MD5_LEN 33         // MD5字符串长度32+1

// 缓存文件头结构
typedef struct {
    uint32_t magic;           // 魔数标识 0x43414348 ("CACH")
    uint32_t version;         // 版本号
    uint64_t timestamp;       // 创建时间戳
    uint32_t sample_rate;     // 采样率
    uint32_t channels;        // 通道数
    uint32_t total_frames;    // 总帧数
    uint32_t song_length;     // 歌曲长度(秒)
    char md5[MAX_MD5_LEN];    // MD5值
    uint32_t frame_count;     // 音频帧数量
    uint32_t reserved[8];     // 保留字段
} cache_file_header_t;

// 音频帧头结构
typedef struct {
    uint32_t frame_size;      // 帧大小
} audio_frame_header_t;

// 缓存文件信息
typedef struct {
    char filename[MAX_FILENAME_LEN];
    char md5[MAX_MD5_LEN];
    uint64_t timestamp;
    uint32_t file_size;
    struct cache_file_info *next;
} cache_file_info_t;

// 缓存管理器
typedef struct {
    bool initialized;
    uint64_t total_cache_size;
    uint32_t file_count;
    cache_file_info_t *file_list_head;
} cache_manager_t;

// 缓存写入句柄
typedef struct {
    FILE *file;
    char filepath[256];
    char md5[MAX_MD5_LEN];
    cache_file_header_t header;
    uint32_t frames_written;
    bool header_written;
} cache_write_handle_t;

// 缓存读取句柄
typedef struct {
    FILE *file;
    cache_file_header_t header;
    uint32_t current_frame;
    bool header_read;
} cache_read_handle_t;

// 初始化和清理函数
esp_err_t song_cache_init(void);
esp_err_t song_cache_deinit(void);

// 缓存检查函数
bool song_cache_exists(const char *md5);
uint64_t song_cache_get_free_space(void);
uint64_t song_cache_get_used_space(void);

// 缓存写入函数
esp_err_t song_cache_start_write(const char *md5, uint32_t sample_rate, 
                                uint32_t channels, uint32_t total_frames, 
                                uint32_t song_length, cache_write_handle_t *handle);
esp_err_t song_cache_write_frame(cache_write_handle_t *handle, 
                                const uint8_t *frame_data, uint32_t frame_size);
esp_err_t song_cache_finish_write(cache_write_handle_t *handle);
esp_err_t song_cache_cancel_write(cache_write_handle_t *handle);

// 缓存读取函数
esp_err_t song_cache_start_read(const char *md5, cache_read_handle_t *handle);
esp_err_t song_cache_read_frame(cache_read_handle_t *handle, 
                               uint8_t *frame_data, uint32_t *frame_size);
esp_err_t song_cache_finish_read(cache_read_handle_t *handle);

// 缓存管理函数
esp_err_t song_cache_cleanup_space(uint64_t required_space);
esp_err_t song_cache_delete_file(const char *md5);
esp_err_t song_cache_get_file_info(const char *md5, cache_file_header_t *header);

// 测试函数
esp_err_t song_cache_test_file_creation(void);

// 内部辅助函数
static esp_err_t _create_cache_directory(void);
static esp_err_t _scan_cache_files(void);
static esp_err_t _add_file_to_list(const char *filename, uint64_t timestamp, uint32_t file_size);
static esp_err_t _remove_file_from_list(const char *md5);
static cache_file_info_t* _find_oldest_file(void);
static void _generate_cache_filepath(const char *md5, char *filepath, size_t filepath_size);

#endif // SONG_CACHE_H