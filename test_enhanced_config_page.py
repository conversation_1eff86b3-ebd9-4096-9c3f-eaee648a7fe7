#!/usr/bin/env python3
"""
测试增强的配网页面功能
验证状态轮询和实时反馈是否正常工作
"""

import requests
import time
import webbrowser
import sys
from urllib.parse import urljoin

DEVICE_IP = "************"
BASE_URL = f"http://{DEVICE_IP}"

def check_device_reachable():
    """检查设备是否可达"""
    try:
        response = requests.get(f"{BASE_URL}/api/device/info", timeout=3)
        return response.status_code == 200
    except:
        return False

def test_config_page():
    """测试配网页面"""
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✓ 配网页面可访问")
            print(f"  页面大小: {len(response.text)} 字节")
            
            # 检查页面内容
            content = response.text.lower()
            features = [
                ("智能配网标题", "wifi speaker config" in content),
                ("状态显示区域", "id='status'" in content),
                ("进度条", "progress" in content),
                ("JavaScript脚本", "config.js" in content),
                ("响应式设计", "viewport" in content),
                ("现代样式", "border-radius" in content)
            ]
            
            for feature, exists in features:
                status = "✓" if exists else "✗"
                print(f"  {status} {feature}")
            
            return True
        else:
            print(f"✗ 配网页面访问失败 (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"✗ 配网页面访问失败: {e}")
        return False

def test_javascript_file():
    """测试JavaScript文件"""
    try:
        response = requests.get(f"{BASE_URL}/config.js", timeout=5)
        if response.status_code == 200:
            print("✓ JavaScript文件可访问")
            print(f"  文件大小: {len(response.text)} 字节")
            print(f"  内容类型: {response.headers.get('Content-Type', 'N/A')}")
            
            # 检查JavaScript功能
            content = response.text
            js_features = [
                ("状态轮询函数", "pollWiFiStatus" in content),
                ("连接函数", "connectWiFi" in content),
                ("状态显示函数", "showStatus" in content),
                ("进度条函数", "showProgress" in content),
                ("按钮状态控制", "setButtonState" in content),
                ("事件监听器", "addEventListener" in content),
                ("错误处理", "catch" in content)
            ]
            
            for feature, exists in js_features:
                status = "✓" if exists else "✗"
                print(f"  {status} {feature}")
            
            return True
        else:
            print(f"✗ JavaScript文件访问失败 (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"✗ JavaScript文件访问失败: {e}")
        return False

def test_api_endpoints():
    """测试API端点"""
    endpoints = [
        ("/api/device/info", "GET", "设备信息"),
        ("/api/wifi/status", "GET", "WiFi状态"),
        ("/api/wifi/scan", "GET", "WiFi扫描")
    ]
    
    print("测试API端点...")
    for endpoint, method, description in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{BASE_URL}{endpoint}", timeout=5)
            else:
                response = requests.post(f"{BASE_URL}{endpoint}", timeout=5)
            
            if response.status_code == 200:
                print(f"  ✓ {description} ({endpoint})")
            else:
                print(f"  ✗ {description} ({endpoint}) - 状态码: {response.status_code}")
        except Exception as e:
            print(f"  ✗ {description} ({endpoint}) - 错误: {e}")

def simulate_wifi_config():
    """模拟WiFi配置请求"""
    print("\n模拟WiFi配置请求...")
    
    # 使用无效的WiFi配置进行测试
    test_data = {
        "ssid": "TEST_WIFI_" + str(int(time.time())),
        "password": "test_password"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/wifi/config",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ WiFi配置请求成功")
            print(f"  响应: {result.get('message', 'N/A')}")
            
            # 测试状态轮询
            print("  测试状态轮询...")
            for i in range(5):
                time.sleep(1)
                try:
                    status_response = requests.get(f"{BASE_URL}/api/wifi/status", timeout=3)
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        wifi_status = status_data.get('data', {}).get('status', 'unknown')
                        print(f"    轮询 {i+1}: 状态 = {wifi_status}")
                        
                        if wifi_status == 'failed':
                            print("  ✓ 检测到连接失败状态（符合预期）")
                            break
                    else:
                        print(f"    轮询 {i+1}: 状态查询失败")
                except:
                    print(f"    轮询 {i+1}: 网络错误")
            
            return True
        else:
            print(f"✗ WiFi配置请求失败 (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"✗ WiFi配置请求失败: {e}")
        return False

def open_config_page():
    """打开配网页面"""
    config_url = f"{BASE_URL}/"
    print(f"\n尝试在浏览器中打开配网页面: {config_url}")
    
    try:
        webbrowser.open(config_url)
        print("✓ 已在默认浏览器中打开配网页面")
        print("\n请在浏览器中测试以下功能:")
        print("1. 页面样式是否美观")
        print("2. 输入WiFi名称和密码")
        print("3. 点击连接按钮")
        print("4. 观察状态变化和进度条")
        print("5. 验证错误处理")
        return True
    except Exception as e:
        print(f"✗ 无法打开浏览器: {e}")
        print(f"请手动访问: {config_url}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("增强配网页面功能测试")
    print("=" * 60)
    
    # 1. 检查设备连接
    print("1. 检查设备连接...")
    if not check_device_reachable():
        print("✗ 无法连接到设备，请确认：")
        print("  - 已连接到设备热点 WifiSpeaker-XXXXXX")
        print("  - 设备IP地址正确 (************)")
        return
    print("✓ 设备连接正常")
    
    # 2. 测试配网页面
    print("\n2. 测试配网页面...")
    page_ok = test_config_page()
    
    # 3. 测试JavaScript文件
    print("\n3. 测试JavaScript文件...")
    js_ok = test_javascript_file()
    
    # 4. 测试API端点
    print("\n4. 测试API端点...")
    test_api_endpoints()
    
    # 5. 模拟WiFi配置
    print("\n5. 模拟WiFi配置...")
    config_ok = simulate_wifi_config()
    
    # 6. 打开配网页面进行手动测试
    print("\n6. 手动测试...")
    browser_ok = open_config_page()
    
    # 总结
    print("\n" + "=" * 60)
    print("测试总结:")
    print(f"  配网页面: {'✓' if page_ok else '✗'}")
    print(f"  JavaScript: {'✓' if js_ok else '✗'}")
    print(f"  WiFi配置: {'✓' if config_ok else '✗'}")
    print(f"  浏览器测试: {'✓' if browser_ok else '✗'}")
    
    if all([page_ok, js_ok, config_ok]):
        print("\n✓ 所有自动测试通过！")
        print("新的配网页面功能:")
        print("  • 实时状态反馈")
        print("  • 智能进度显示")
        print("  • 错误处理和重试")
        print("  • 响应式设计")
        print("  • 用户友好的界面")
    else:
        print("\n✗ 部分测试失败，请检查设备固件")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
