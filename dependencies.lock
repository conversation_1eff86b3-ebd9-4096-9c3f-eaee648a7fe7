dependencies:
  espressif/cmake_utilities:
    component_hash: 351350613ceafba240b761b4ea991e0f231ac7a9f59a9ee901f751bddc0bb18f
    dependencies:
    - name: idf
      require: private
      version: '>=4.1'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 0.5.3
  espressif/esp-dsp:
    component_hash: 3e7bbd487f1357a1d4944d0c85966d049501ea281b8a4c7f93f7cfedd5b7f23d
    dependencies:
    - name: idf
      require: private
      version: '>=4.2'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 1.4.12
  espressif/iot_eth:
    dependencies:
    - name: idf
      version: '>=4.4'
    - name: espressif/cmake_utilities
      version: '*'
    source:
      path: D:\project\esp32\esp-idf\esp-iot-solution\components\iot_eth
      type: local
    version: 0.1.0
  espressif/iot_usbh_cdc:
    dependencies:
    - name: idf
      version: '>=4.4.1'
    - name: espressif/cmake_utilities
      version: '*'
    source:
      path: D:\project\esp32\esp-idf\esp-iot-solution\components\usb\iot_usbh_cdc
      type: local
    targets:
    - esp32s2
    - esp32s3
    - esp32p4
    version: 2.0.0
  espressif/jsmn:
    component_hash: d80350c41bbaa827c98a25b6072df00884e72f54885996fab4a4f0aebce6b6c3
    dependencies:
    - name: idf
      require: private
      version: '>=4.3'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 1.1.0
  espressif/nghttp:
    component_hash: c52b83cc3d1083ae669751534ea4f7445974b86d91d49a5273bdb0fbf87f34d6
    dependencies:
    - name: idf
      require: private
      version: '>=5.0'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 1.62.1
  espressif/usb_host_msc:
    component_hash: efbf44743b0f1f1f808697a671064531ae4661ccbce84632637261f8f670b375
    dependencies:
    - name: idf
      require: private
      version: '>=4.4.1'
    source:
      registry_url: https://components.espressif.com/
      type: service
    targets:
    - esp32s2
    - esp32s3
    - esp32p4
    version: 1.1.3
  espressif/zlib:
    component_hash: d901723af51f13fc8e5824f39f32239c847956e8fd951a05266588dc5cfbb9ae
    dependencies:
    - name: idf
      require: private
      version: '>=4.4'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 1.3.1
  idf:
    source:
      type: idf
    version: 5.4.0
  iot_usbh_cdc:
    dependencies: []
    source:
      path: D:\project\esp32\esp-idf\esp-iot-solution\components\usb\iot_usbh_cdc
      type: local
    version: 2.0.0
  leeebo/esp-inih:
    component_hash: a2ecd9643d4cf7b3592645579d6ecf0f5c4f07f8ca8f9bafeb1d0f89fd005f38
    dependencies:
    - name: idf
      require: private
      version: '>=4.4'
    source:
      registry_url: https://components.espressif.com/
      type: service
    version: 0.0.2
  leeebo/tinyusb_src:
    component_hash: f8a386b696474c2b20482b662319200e4a2ce9cae8fa3ae7830d3929f03780c8
    dependencies:
    - name: idf
      require: private
      version: '>=4.4'
    source:
      registry_url: https://components.espressif.com/
      type: service
    targets:
    - esp32s2
    - esp32s3
    - esp32p4
    version: 0.16.0~6
direct_dependencies:
- espressif/cmake_utilities
- espressif/esp-dsp
- espressif/iot_eth
- espressif/iot_usbh_cdc
- espressif/jsmn
- espressif/nghttp
- espressif/usb_host_msc
- espressif/zlib
- idf
- iot_usbh_cdc
- leeebo/esp-inih
- leeebo/tinyusb_src
manifest_hash: 54b009275d79634e8041da606cca81bb65d8fb3527897fde9761c28ce81d635b
target: esp32s3
version: 2.0.0
