#include "nvs.h"
#include "esp_log.h"
#include "sysconf.h"

static const char *TAG = "sysconf";

//设备号
char g_device_code[48];
//系统时间戳
char g_sys_timeStamp[16];

char g_mqtt_server[64];
int g_mqtt_port;
char g_tcp_server[64];
int g_tcp_port;

//MQTT订阅主题(发布主题固定)
char mqtt_sub_topic_name[96];

unsigned char g_system_volume=100;    //系统音量

char g_soundCard_server_Addr[64];    //声卡服务器地址
short int g_soundCard_server_port=8688;         //声卡服务器端口


#define NVS_NAMESPACE_BASIC  "basic_config"

//保存配置
void save_basic_config()
{
    nvs_handle_t nvs_handle;
    esp_err_t err=nvs_open(NVS_NAMESPACE_BASIC, NVS_READWRITE, &nvs_handle);
    if (err == ESP_OK)  
    {
        nvs_set_u8(nvs_handle, "System_Volume", g_system_volume);
        #if 0
        if(nvs_set_blob(nvs_handle, "SoundCard_Addr", g_soundCard_server_Addr, strlen(g_soundCard_server_Addr)+1)!=ESP_OK) {
            ESP_LOGE(TAG, "Failed to save SoundCard_Addr");
        }
        if(nvs_set_i16(nvs_handle, "SoundCard_Port", g_soundCard_server_port)!=ESP_OK) {
            ESP_LOGE(TAG, "Failed to save SoundCard_Port"); 
        }
        nvs_commit(nvs_handle);
        nvs_close(nvs_handle);
        #endif
        ESP_LOGI(TAG, "save basic config succeed!");
    }
    else {
        ESP_LOGE(TAG, "Failed to open NVS (%s)", esp_err_to_name(err)); 
    }
}


//读取配置
void read_basic_config()
{
    nvs_handle_t nvs_handle;
    esp_err_t err=nvs_open(NVS_NAMESPACE_BASIC, NVS_READONLY, &nvs_handle);
    if (err == ESP_OK)
    {
        nvs_get_u8(nvs_handle, "System_Volume", &g_system_volume);
#if 0
        size_t soundCardServerAddr_len = sizeof(g_soundCard_server_Addr);
        nvs_get_blob(nvs_handle, "SoundCard_Addr", g_soundCard_server_Addr, &soundCardServerAddr_len);
        nvs_get_i16(nvs_handle, "SoundCard_Port", &g_soundCard_server_port);
#endif
        nvs_close(nvs_handle);
    }
    else {
        ESP_LOGE(TAG, "Failed to open NVS (%s)", esp_err_to_name(err)); 
    }
}