# WiFi Speaker SoftAP 配网 API 接口文档

## 概述

本文档描述了WiFi Speaker设备在SoftAP配网模式下提供的HTTP API接口。设备在配网模式下会创建一个WiFi热点，用户连接后可通过这些API接口进行设备配置和状态查询。

## 基础信息

- **设备IP地址**: `************`
- **HTTP端口**: `80`
- **基础URL**: `http://************`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 通用响应格式

所有API接口都使用统一的JSON响应格式：

```json
{
    "code": 200,
    "message": "Success",
    "data": {}
}
```

### 响应状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

## API 接口列表

### 1. 获取设备信息

获取设备的基本信息，包括设备码、版本信息、MAC地址等。

**请求信息**
- **URL**: `/api/device/info`
- **方法**: `GET`
- **参数**: 无

**请求示例**
```bash
curl -X GET http://************/api/device/info
```

**响应示例**
```json
{
    "code": 200,
    "message": "Success",
    "data": {
        "deviceCode": "26985526807960",
        "deviceName": "WifiSpeakerA",
        "firmwareVersion": "P201_WIFI_V02",
        "projectVersion": "V2.5.0623",
        "macAddress": "24:6f:28:12:34:56",
        "wifiConnected": false,
        "currentSSID": "",
        "rssi": 0
    }
}
```

**响应字段说明**
| 字段 | 类型 | 说明 |
|------|------|------|
| deviceCode | string | 设备唯一标识码 |
| deviceName | string | 设备名称 |
| firmwareVersion | string | 固件版本号 |
| projectVersion | string | 项目版本号 |
| macAddress | string | 设备MAC地址 |
| wifiConnected | boolean | WiFi连接状态 |
| currentSSID | string | 当前连接的WiFi名称 |
| rssi | number | WiFi信号强度 |

### 2. WiFi配置

配置设备连接到指定的WiFi网络。

**请求信息**
- **URL**: `/api/wifi/config`
- **方法**: `POST`
- **内容类型**: `application/json`

**请求参数**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| ssid | string | 是 | WiFi网络名称 |
| password | string | 否 | WiFi密码（开放网络可为空） |

**请求示例**
```bash
curl -X POST http://************/api/wifi/config \
  -H "Content-Type: application/json" \
  -d '{
    "ssid": "MyHomeWiFi",
    "password": "mypassword123"
  }'
```

**响应示例**
```json
{
    "code": 200,
    "message": "WiFi configuration started",
    "data": null
}
```

**错误响应示例**
```json
{
    "code": 400,
    "message": "Invalid SSID",
    "data": null
}
```

### 3. WiFi状态查询

查询当前WiFi连接状态和配置进度。

**请求信息**
- **URL**: `/api/wifi/status`
- **方法**: `GET`
- **参数**: 无

**请求示例**
```bash
curl -X GET http://************/api/wifi/status
```

**响应示例**
```json
{
    "code": 200,
    "message": "Success",
    "data": {
        "status": "connected",
        "connected": true,
        "ssid": "MyHomeWiFi",
        "rssi": -45,
        "ip": "*************"
    }
}
```

**状态字段说明**
| 状态值 | 说明 |
|--------|------|
| idle | 空闲状态，未开始配网 |
| connecting | 正在连接WiFi |
| connected | WiFi连接成功 |
| failed | WiFi连接失败 |

### 4. WiFi网络扫描

扫描附近可用的WiFi网络。

**请求信息**
- **URL**: `/api/wifi/scan`
- **方法**: `GET`
- **参数**: 无

**请求示例**
```bash
curl -X GET http://************/api/wifi/scan
```

**响应示例**
```json
{
    "code": 200,
    "message": "Success",
    "data": [
        {
            "ssid": "HomeWiFi",
            "rssi": -35,
            "authmode": 3,
            "authType": "WPA2 PSK"
        },
        {
            "ssid": "OfficeWiFi",
            "rssi": -55,
            "authmode": 4,
            "authType": "WPA/WPA2 PSK"
        },
        {
            "ssid": "PublicWiFi",
            "rssi": -70,
            "authmode": 0,
            "authType": "Open"
        }
    ]
}
```

**WiFi网络字段说明**
| 字段 | 类型 | 说明 |
|------|------|------|
| ssid | string | WiFi网络名称 |
| rssi | number | 信号强度（dBm） |
| authmode | number | 加密模式代码 |
| authType | string | 加密类型描述 |

**加密类型对照表**
| authmode | authType | 说明 |
|----------|----------|------|
| 0 | Open | 开放网络 |
| 1 | WEP | WEP加密 |
| 2 | WPA PSK | WPA个人版 |
| 3 | WPA2 PSK | WPA2个人版 |
| 4 | WPA/WPA2 PSK | WPA/WPA2混合 |
| 5 | WPA2 Enterprise | WPA2企业版 |
| 6 | WPA3 PSK | WPA3个人版 |
| 7 | WPA2/WPA3 PSK | WPA2/WPA3混合 |



## 使用流程

### 典型配网流程

1. **连接设备热点**
   ```
   SSID: WifiSpeaker-XXXXXX
   密码: wifi87654321
   ```

2. **获取设备信息**
   ```bash
   curl -X GET http://************/api/device/info
   ```

3. **扫描WiFi网络（可选）**
   ```bash
   curl -X GET http://************/api/wifi/scan
   ```

4. **配置WiFi**
   ```bash
   curl -X POST http://************/api/wifi/config \
     -H "Content-Type: application/json" \
     -d '{"ssid": "MyWiFi", "password": "mypassword"}'
   ```

5. **轮询连接状态**
   ```bash
   # 每2秒查询一次状态，直到连接成功或失败
   curl -X GET http://************/api/wifi/status
   ```

6. **配网完成**
   - 连接成功：设备自动关闭SoftAP模式
   - 连接失败：可重新配置

## 错误处理

### 常见错误码

| 错误码 | 错误信息 | 解决方案 |
|--------|----------|----------|
| 400 | Invalid SSID | 检查WiFi名称是否正确 |
| 400 | Invalid JSON format | 检查请求JSON格式 |
| 500 | Failed to configure WiFi | 检查设备状态，重试配置 |
| 500 | Failed to get device info | 设备内部错误，重启设备 |


## 安全注意事项

1. **网络安全**: SoftAP模式仅用于配网，配网完成后会自动关闭
2. **密码保护**: 设备热点使用固定密码保护
3. **访问限制**: API仅在配网模式下可用
4. **数据验证**: 所有输入参数都会进行验证


### 常用调试命令

```bash
# 检查设备连通性
ping ************

# 测试所有API接口
curl -X GET http://************/api/device/info
curl -X GET http://************/api/wifi/status
curl -X GET http://************/api/wifi/scan

# 测试配网功能
curl -X POST http://************/api/wifi/config \
  -H "Content-Type: application/json" \
  -d '{"ssid": "TestWiFi", "password": "testpass"}'
```

## 版本信息

- **API版本**: 1.0
- **文档版本**: 1.0
- **最后更新**: 2025年6月23日
