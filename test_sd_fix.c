/*
 * 测试SD卡修复的简单程序
 * 编译并运行此程序来验证SD卡文件创建功能
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <errno.h>
#include <unistd.h>

#define CACHE_MOUNT_POINT "/sdcard"
#define CACHE_DIR_PATH "/sdcard/cache"

// 测试SD卡文件创建功能
int test_file_creation(void)
{
    printf("Testing SD card file creation...\n");
    
    // 检查挂载点
    struct stat mount_stat;
    if (stat(CACHE_MOUNT_POINT, &mount_stat) != 0) {
        printf("ERROR: Mount point does not exist: %s\n", CACHE_MOUNT_POINT);
        return -1;
    }
    printf("Mount point exists: %s (permissions: 0%o)\n", 
           CACHE_MOUNT_POINT, mount_stat.st_mode & 0777);
    
    // 创建缓存目录
    struct stat cache_stat;
    if (stat(CACHE_DIR_PATH, &cache_stat) == -1) {
        printf("Creating cache directory: %s\n", CACHE_DIR_PATH);
        if (mkdir(CACHE_DIR_PATH, 0755) != 0) {
            printf("ERROR: Failed to create cache directory: %s (errno: %d)\n", 
                   strerror(errno), errno);
            return -1;
        }
        printf("Cache directory created successfully\n");
    } else {
        printf("Cache directory already exists: %s\n", CACHE_DIR_PATH);
    }
    
    // 创建测试文件
    char test_filepath[256];
    snprintf(test_filepath, sizeof(test_filepath), "%s/test_creation.tmp", CACHE_DIR_PATH);
    
    FILE *test_file = fopen(test_filepath, "w");
    if (!test_file) {
        printf("ERROR: Failed to create test file: %s (errno: %d, %s)\n", 
               test_filepath, errno, strerror(errno));
        return -1;
    }
    
    // 写入测试数据
    const char *test_data = "This is a test file for SD card functionality";
    size_t data_len = strlen(test_data);
    
    if (fwrite(test_data, 1, data_len, test_file) != data_len) {
        printf("ERROR: Failed to write test data: %s\n", strerror(errno));
        fclose(test_file);
        remove(test_filepath);
        return -1;
    }
    
    fclose(test_file);
    
    // 验证文件存在
    struct stat st;
    if (stat(test_filepath, &st) != 0) {
        printf("ERROR: Test file does not exist after creation\n");
        return -1;
    }
    
    // 验证文件大小
    if (st.st_size != data_len) {
        printf("ERROR: Test file size mismatch: expected %zu, got %ld\n", 
               data_len, st.st_size);
        remove(test_filepath);
        return -1;
    }
    
    // 读取并验证数据
    test_file = fopen(test_filepath, "r");
    if (!test_file) {
        printf("ERROR: Failed to reopen test file for reading\n");
        remove(test_filepath);
        return -1;
    }
    
    char read_buffer[256];
    size_t read_size = fread(read_buffer, 1, sizeof(read_buffer) - 1, test_file);
    fclose(test_file);
    
    if (read_size != data_len || memcmp(read_buffer, test_data, data_len) != 0) {
        printf("ERROR: Test file data verification failed\n");
        remove(test_filepath);
        return -1;
    }
    
    // 清理测试文件
    if (remove(test_filepath) != 0) {
        printf("WARNING: Failed to remove test file: %s\n", strerror(errno));
    }
    
    printf("SUCCESS: SD card file creation test passed!\n");
    return 0;
}

int main(void)
{
    printf("=== SD Card File Creation Test ===\n");
    
    int result = test_file_creation();
    
    if (result == 0) {
        printf("All tests passed successfully!\n");
    } else {
        printf("Tests failed!\n");
    }
    
    return result;
}
