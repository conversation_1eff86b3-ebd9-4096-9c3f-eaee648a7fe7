#include "esp_mac.h"
#include "string.h"
#include "stdlib.h"
#include "mbedtls/md5.h"
#include "esp_system.h"
#include "sysMethod.h"

uint64_t  get_esp32_uuid() {
    uint8_t mac[6];
    esp_efuse_mac_get_default(mac);
    return ((uint64_t)mac[0] << 40) | ((uint64_t)mac[1] << 32) |
           ((uint64_t)mac[2] << 24) | ((uint64_t)mac[3] << 16) |
           ((uint64_t)mac[4] << 8) | mac[5];
}


void calculate_md5(const char *input_str, char *output_str) {
    // 创建 MD5 上下文
    mbedtls_md5_context ctx;
    mbedtls_md5_init(&ctx);

    // 启动 MD5 计算
    mbedtls_md5_starts(&ctx);

    // 更新 MD5 计算，传入输入字符串
    mbedtls_md5_update(&ctx, (const unsigned char *)input_str, strlen(input_str));

    // 完成 MD5 计算，得到哈希值
    unsigned char hash[16];
    mbedtls_md5_finish(&ctx, hash);

    // 清理上下文
    mbedtls_md5_free(&ctx);

    // 将哈希值转换为十六进制字符串
    for (int i = 0; i < 16; i++) {
        sprintf(output_str + i * 2, "%02x", hash[i]);
    }
}