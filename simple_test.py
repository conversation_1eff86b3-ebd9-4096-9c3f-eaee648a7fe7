#!/usr/bin/env python3
"""
简单的WiFi Speaker配网测试脚本
"""

import requests
import json

DEVICE_IP = "************"
BASE_URL = f"http://{DEVICE_IP}"

def test_basic_connection():
    """测试基本连接"""
    print("测试基本连接...")
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"✓ 连接成功 (状态码: {response.status_code})")
        return True
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False

def test_device_info():
    """测试设备信息"""
    print("测试设备信息...")
    try:
        response = requests.get(f"{BASE_URL}/api/device/info", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 设备信息获取成功")
            print(f"  设备码: {data.get('data', {}).get('deviceCode', 'N/A')}")
            print(f"  设备名: {data.get('data', {}).get('deviceName', 'N/A')}")
            return True
        else:
            print(f"✗ 设备信息获取失败 (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"✗ 设备信息获取失败: {e}")
        return False

def test_wifi_status():
    """测试WiFi状态"""
    print("测试WiFi状态...")
    try:
        response = requests.get(f"{BASE_URL}/api/wifi/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✓ WiFi状态获取成功")
            print(f"  状态: {data.get('data', {}).get('status', 'N/A')}")
            print(f"  已连接: {data.get('data', {}).get('connected', False)}")
            return True
        else:
            print(f"✗ WiFi状态获取失败 (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"✗ WiFi状态获取失败: {e}")
        return False

def test_wifi_config(ssid, password):
    """测试WiFi配置"""
    print(f"测试WiFi配置 (SSID: {ssid})...")
    try:
        data = {"ssid": ssid, "password": password}
        response = requests.post(
            f"{BASE_URL}/api/wifi/config", 
            json=data, 
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        if response.status_code == 200:
            result = response.json()
            print(f"✓ WiFi配置成功: {result.get('message', 'OK')}")
            return True
        else:
            print(f"✗ WiFi配置失败 (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"✗ WiFi配置失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("WiFi Speaker 简单测试")
    print("=" * 50)
    
    # 基本测试
    tests = [
        test_basic_connection,
        test_device_info,
        test_wifi_status
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"基本测试结果: {passed}/{len(tests)} 通过")
    
    # WiFi配置测试（需要用户输入）
    if passed == len(tests):
        print("\n" + "=" * 30)
        print("WiFi配置测试 (可选)")
        print("=" * 30)
        
        ssid = input("请输入WiFi名称 (留空跳过): ").strip()
        if ssid:
            password = input("请输入WiFi密码: ").strip()
            test_wifi_config(ssid, password)
            
            print("\n注意: 配网成功后设备将连接到新WiFi并关闭热点")
            print("请等待设备重新连接到您的WiFi网络")
        else:
            print("跳过WiFi配置测试")
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()
