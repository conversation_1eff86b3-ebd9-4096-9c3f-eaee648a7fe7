#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "audio_element.h"
#include "audio_pipeline.h"
#include "audio_event_iface.h"
#include "i2s_stream.h"
#include "g722_decoder.h"
#include "audio.h"
#include "sysconf.h"

static const char *TAG = "G722_PLAYER";

// G.722解码器元素
typedef struct {
    G722_DEC_CTX *g722_dctx;
    short *g722_obuf;

} g722_decoder_t;


static esp_err_t g722_el_open(audio_element_handle_t self) {
    ESP_LOGI(TAG, "g722_el_open");
    return ESP_OK;
}

// G.722解码回调函数
static int g722_decode_process(audio_element_handle_t self, char *in_buffer, int in_len)
{
    //ESP_LOGI(TAG, "g722_decode_process,in_len=%d",in_len);
    g722_decoder_t *g722 = (g722_decoder_t *)audio_element_getdata(self);
    
    int rsize = audio_element_input(self, in_buffer, in_len);   // 读取输入数据
    //ESP_LOGI(TAG, "g722_decode_process2:,rsize=%d",rsize);
    // G.722解码 - 每个G.722字节产生两个PCM样本(16位)
    int samples = g722_decode(g722->g722_dctx, (const uint8_t *)in_buffer, rsize, (int16_t *)g722->g722_obuf);
    //ESP_LOGI(TAG, "g722_decode_process3:,samples=%d",samples);
    int w_size = samples * sizeof(int16_t);  // 16位PCM

    audio_element_output(self, (char *)g722->g722_obuf, w_size);
    
    return w_size;
}

// 创建G.722解码器元素
static audio_element_handle_t g722_decoder_init(void)
{
    g722_decoder_t *g722 = calloc(1, sizeof(g722_decoder_t));
    AUDIO_MEM_CHECK(TAG, g722, return NULL);

    //初始化G722
    int srate = ~G722_SAMPLE_RATE_8000;
    g722->g722_obuf = (short *)malloc(256 * 4);
    if (g722->g722_obuf == NULL)
    {
        ESP_LOGI(TAG,"g722_obuf malloc failed");
        return NULL;
    }
    g722->g722_dctx = g722_decoder_new(64000, srate);
    if (g722->g722_dctx == NULL)
    {
        ESP_LOGI(TAG,"g722_decoder_new() failed");
        free(g722->g722_obuf);
        g722->g722_obuf = NULL;
        return NULL;
    }

    
    audio_element_cfg_t cfg = DEFAULT_AUDIO_ELEMENT_CONFIG();
    cfg.tag = "g722";
    cfg.open = g722_el_open;
    cfg.process = g722_decode_process;
    
    audio_element_handle_t el = audio_element_init(&cfg);
    AUDIO_MEM_CHECK(TAG, el, {
        free(g722->g722_obuf);
        g722_decoder_destroy(g722->g722_dctx);
        free(g722);
        return NULL;
    });
    
    audio_element_setdata(el, g722);
    return el;
}

// 从环形缓冲区读取G.722数据的回调
static int g722_read_callback(audio_element_handle_t el, char *buffer, int len, TickType_t timeout, void *ctx) {
    const int fixed_size = 256;  // 固定读取256字节
    
    // 从环形缓冲区读取G.722编码数据
    int bytes_read = rb_read(audio_data_rb, buffer, fixed_size, 0xFFFFFFFF);
    
    // 处理数据不足的情况
    if (bytes_read < fixed_size) {
        memset(buffer + bytes_read, 0, fixed_size - bytes_read);
        ESP_LOGW(TAG, "Underrun: %d bytes missing", fixed_size - bytes_read);
    }
    return fixed_size;
}

// I2S写入回调，用于音量控制
static int g722_i2s_write_cb(audio_element_handle_t el, char *buf, int len, TickType_t wait_time, void *ctx)
{
    stream_func i2s_write_func = (stream_func)ctx;
    size_t size = len;

    float volumeGain = g_system_volume/100.0; // 音量调整因子
    // 16位PCM数据
    int16_t *pcm = (int16_t *)buf;
    int samples = len / sizeof(int16_t);
    
    for(int i = 0; i < samples; i++) {
        float sample = pcm[i] * volumeGain;
        // 限制幅度在16位范围内
        pcm[i] = (int16_t)(sample > 32767 ? 32767 : (sample < -32768 ? -32768 : sample));
    }
    
    int ret = i2s_write_func(el, buf, size, wait_time, ctx);
    if (ret < 0) {
        ESP_LOGE(TAG, "i2s write failed");
    }
    return len;
}


void g722_play_stream(void *arg)
{
    if(g_playTaskSrc != TASK_SRC_PAGING) {
        vTaskDelete(NULL);
    }

    //初始化ringbuf
    ringbuf_init(RINGBUF_G722_BLOCK_SIZE, RINGBUF_G722_BLOCKS_NUM);

    audio_pipeline_handle_t pipeline;
    audio_element_handle_t i2s_stream_writer, g722_decoder_el;

    ESP_LOGI(TAG, "[1.0] Create audio pipeline for G.722 playback");
    audio_pipeline_cfg_t pipeline_cfg = DEFAULT_AUDIO_PIPELINE_CONFIG();
    pipeline = audio_pipeline_init(&pipeline_cfg);
    mem_assert(pipeline);

    ESP_LOGI(TAG, "[1.1] Create i2s stream to write data to codec chip");
    i2s_stream_cfg_t i2s_cfg = I2S_STREAM_CFG_DEFAULT();
    i2s_cfg.type = AUDIO_STREAM_WRITER;
    i2s_cfg.chan_cfg.dma_frame_num = 256;   // 512/16000=16ms (G.722采样率通常为16kHz)
    i2s_cfg.chan_cfg.dma_desc_num = 4;      // 4*16ms=64ms
    
    i2s_stream_writer = i2s_stream_init(&i2s_cfg);
    audio_element_set_write_cb(i2s_stream_writer, g722_i2s_write_cb, audio_element_get_write_cb(i2s_stream_writer));

    // 设置I2S时钟 - G.722通常使用16kHz采样率，16位，单声道或双声道
    i2s_stream_set_clk(i2s_stream_writer, 16000, 16, g_audio_channel_num);

    ESP_LOGI(TAG, "[1.2] Create G.722 decoder");
    g722_decoder_el = g722_decoder_init();
    audio_element_set_read_cb(g722_decoder_el, g722_read_callback, NULL);

    ESP_LOGI(TAG, "[1.3] Register all elements to audio pipeline");
    audio_pipeline_register(pipeline, g722_decoder_el, "g722");
    audio_pipeline_register(pipeline, i2s_stream_writer, "i2s");

    ESP_LOGI(TAG, "[1.4] Link elements together: g722_decoder-->i2s_stream");
    const char *link_tag[] = {"g722", "i2s"};
    audio_pipeline_link(pipeline, &link_tag[0], 2);
    
    // 添加事件监听
    ESP_LOGI(TAG, "[2.0] Set up event listener");
    audio_event_iface_cfg_t evt_cfg = AUDIO_EVENT_IFACE_DEFAULT_CFG();
    audio_event_iface_handle_t evt = audio_event_iface_init(&evt_cfg);
    audio_pipeline_set_listener(pipeline, evt);

    bool pipeline_running = false;
    while(g_playTaskSrc == TASK_SRC_PAGING)
    {
        // 说明pipeline已经开始运行了
        if(g_stream_ready_play)
        {
            if(!pipeline_running)
            {
                ESP_LOGI(TAG, "Start pipeline!");
                audio_pipeline_run(pipeline);
                pipeline_running = true;
            }
        }

        // 事件处理
        audio_event_iface_msg_t msg;
        if (audio_event_iface_listen(evt, &msg, 0) == ESP_OK) {
            if (msg.source_type == AUDIO_ELEMENT_TYPE_ELEMENT 
                && (msg.source == (void *)i2s_stream_writer || msg.source == (void *)g722_decoder_el)
                && msg.cmd == AEL_MSG_CMD_REPORT_STATUS
                && ((int)msg.data == AEL_STATUS_STATE_STOPPED || (int)msg.data == AEL_STATUS_STATE_FINISHED)) {
                ESP_LOGW(TAG, "[ * ] %s stopped", (msg.source == i2s_stream_writer) ? "I2S" : "G722_DECODER");
                break;
            }
        }
        vTaskDelay(10 / portTICK_PERIOD_MS);
    }
    
    audio_event_iface_destroy(evt);
    audio_pipeline_stop(pipeline);
    
    if(audio_data_rb)
        rb_done_write(audio_data_rb);
    
    audio_pipeline_wait_for_stop(pipeline);
    audio_pipeline_terminate(pipeline);
    audio_pipeline_unregister(pipeline, i2s_stream_writer);
    audio_pipeline_unregister(pipeline, g722_decoder_el);
    audio_pipeline_remove_listener(pipeline);
    // 释放资源
    audio_pipeline_deinit(pipeline);
    audio_element_deinit(i2s_stream_writer);

    // 释放G722解码器元素
    g722_decoder_t *g722 = (g722_decoder_t *)audio_element_getdata(g722_decoder_el);
    if(g722)
    {
        if (g722->g722_obuf)
        {
            free(g722->g722_obuf);
            g722->g722_obuf = NULL;
        }
        if (g722->g722_dctx)
        {
            g722_decoder_destroy(g722->g722_dctx);
            g722->g722_dctx = NULL;
        }
        free(g722);
    }
    audio_element_deinit(g722_decoder_el);
    // 释放ringbuf
    ringbuf_deinit();

    ESP_LOGI(TAG, "g722_play_stream END...");
    vTaskDelete(NULL);
}

void start_g722Player_stream_task() {
    xTaskCreate(g722_play_stream, "g722_player_stream", 8192, NULL, 10, NULL); 
}