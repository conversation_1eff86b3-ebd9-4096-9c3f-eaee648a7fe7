#include "simple_page.h"
#include "esp_log.h"

static const char *TAG = "SimplePage";

// 外部变量声明
extern char g_device_code[64];

// 智能配网页面处理器
esp_err_t simple_config_page_handler(httpd_req_t *req)
{
    httpd_resp_set_type(req, "text/html");

    // 发送HTML头部和样式
    const char* html_head =
        "<!DOCTYPE html>"
        "<html><head>"
        "<title>WiFi Config</title>"
        "<meta charset='UTF-8'>"
        "<meta name='viewport' content='width=device-width,initial-scale=1'>"
        "<style>"
        "body{margin:20px;font-family:Arial;background:#f5f5f5;}"
        ".container{max-width:400px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}"
        "input,button{width:100%;padding:10px;margin:8px 0;border:1px solid #ddd;border-radius:4px;box-sizing:border-box;font-size:16px;}"
        "button{background:#007bff;color:white;border:none;cursor:pointer;transition:background 0.3s;}"
        "button:hover{background:#0056b3;}"
        "button:disabled{background:#ccc;cursor:not-allowed;}"
        ".status{margin:10px 0;padding:10px;border-radius:4px;text-align:center;font-weight:bold;}"
        ".status.connecting{background:#fff3cd;color:#856404;border:1px solid #ffeaa7;}"
        ".status.success{background:#d4edda;color:#155724;border:1px solid #c3e6cb;}"
        ".status.error{background:#f8d7da;color:#721c24;border:1px solid #f5c6cb;}"
        ".status.info{background:#d1ecf1;color:#0c5460;border:1px solid #bee5eb;}"
        ".progress{width:100%;height:4px;background:#e9ecef;border-radius:2px;overflow:hidden;margin:10px 0;}"
        ".progress-bar{height:100%;background:#007bff;width:0%;transition:width 0.3s;}"
        ".hidden{display:none;}"
        "</style>"
        "</head><body>"
        "<div class='container'>"
        "<h2>WiFi Speaker Config</h2>";

    httpd_resp_send_chunk(req, html_head, strlen(html_head));

    // 发送设备信息
    char device_info[128];
    snprintf(device_info, sizeof(device_info),
             "<div class='status info'>Device: %.30s</div>", g_device_code);
    httpd_resp_send_chunk(req, device_info, strlen(device_info));

    // 发送表单
    const char* html_form =
        "<input type='text' id='ssid' placeholder='WiFi Name (SSID)' required>"
        "<input type='password' id='password' placeholder='WiFi Password'>"
        "<button id='connectBtn' onclick='connectWiFi()'>Connect WiFi</button>"
        "<div class='progress hidden' id='progress'>"
        "<div class='progress-bar' id='progressBar'></div>"
        "</div>"
        "<div id='status'></div>"
        "<script src='/config.js'></script>"
        "</div></body></html>";

    httpd_resp_send_chunk(req, html_form, strlen(html_form));

    // 结束响应
    httpd_resp_send_chunk(req, NULL, 0);

    return ESP_OK;
}

// JavaScript配网脚本处理器
esp_err_t config_js_handler(httpd_req_t *req)
{
    httpd_resp_set_type(req, "application/javascript");

    // JavaScript代码 - 第一部分：工具函数
    const char* js_part1 =
        "let pollInterval = null;\n"
        "let pollCount = 0;\n"
        "const maxPollCount = 30;\n"
        "\n"
        "function showStatus(message, type) {\n"
        "    const statusDiv = document.getElementById('status');\n"
        "    statusDiv.className = 'status ' + type;\n"
        "    statusDiv.innerHTML = message;\n"
        "    statusDiv.style.display = 'block';\n"
        "}\n"
        "\n"
        "function showProgress(show, width) {\n"
        "    const progress = document.getElementById('progress');\n"
        "    const progressBar = document.getElementById('progressBar');\n"
        "    if (show) {\n"
        "        progress.classList.remove('hidden');\n"
        "        progressBar.style.width = width + '%';\n"
        "    } else {\n"
        "        progress.classList.add('hidden');\n"
        "    }\n"
        "}\n"
        "\n"
        "function setButtonState(enabled) {\n"
        "    const btn = document.getElementById('connectBtn');\n"
        "    btn.disabled = !enabled;\n"
        "    btn.innerHTML = enabled ? 'Connect WiFi' : 'Connecting...';\n"
        "}\n";

    httpd_resp_send_chunk(req, js_part1, strlen(js_part1));

    // JavaScript代码 - 第二部分：状态轮询
    const char* js_part2 =
        "\n"
        "function pollWiFiStatus() {\n"
        "    fetch('/api/wifi/status')\n"
        "    .then(response => response.json())\n"
        "    .then(data => {\n"
        "        pollCount++;\n"
        "        const progress = Math.min((pollCount / maxPollCount) * 100, 95);\n"
        "        showProgress(true, progress);\n"
        "\n"
        "        if (data.data) {\n"
        "            const status = data.data.status;\n"
        "            const connected = data.data.connected;\n"
        "\n"
        "            if (status === 'connected' && connected) {\n"
        "                clearInterval(pollInterval);\n"
        "                showProgress(true, 100);\n"
        "                showStatus('✓ WiFi connected successfully!<br>Device will now close the hotspot.', 'success');\n"
        "                setButtonState(true);\n"
        "                setTimeout(() => {\n"
        "                    showStatus('Device is connecting to your WiFi network.<br>You can now disconnect from this hotspot.', 'info');\n"
        "                }, 3000);\n"
        "            } else if (status === 'failed') {\n"
        "                clearInterval(pollInterval);\n"
        "                showProgress(false);\n"
        "                showStatus('✗ WiFi connection failed.<br>Please check your WiFi name and password.', 'error');\n"
        "                setButtonState(true);\n"
        "            } else if (status === 'connecting') {\n"
        "                showStatus('🔄 Connecting to WiFi...', 'connecting');\n"
        "            } else {\n"
        "                showStatus('⏳ Preparing connection...', 'connecting');\n"
        "            }\n"
        "        } else {\n"
        "            showStatus('⚠ Unable to get status', 'error');\n"
        "        }\n"
        "\n"
        "        if (pollCount >= maxPollCount) {\n"
        "            clearInterval(pollInterval);\n"
        "            showProgress(false);\n"
        "            showStatus('⏰ Connection timeout. Please try again.', 'error');\n"
        "            setButtonState(true);\n"
        "        }\n"
        "    })\n"
        "    .catch(error => {\n"
        "        console.log('Poll error:', error);\n"
        "        if (pollCount < 5) {\n"
        "            showStatus('🔄 Checking connection...', 'connecting');\n"
        "        } else {\n"
        "            clearInterval(pollInterval);\n"
        "            showProgress(false);\n"
        "            showStatus('✓ Connection may have succeeded.<br>Device hotspot is no longer accessible.', 'success');\n"
        "            setButtonState(true);\n"
        "        }\n"
        "    });\n"
        "}\n";

    httpd_resp_send_chunk(req, js_part2, strlen(js_part2));

    // JavaScript代码 - 第三部分：主要连接函数
    const char* js_part3 =
        "\n"
        "function connectWiFi() {\n"
        "    const ssid = document.getElementById('ssid').value.trim();\n"
        "    const password = document.getElementById('password').value;\n"
        "\n"
        "    if (!ssid) {\n"
        "        showStatus('⚠ Please enter WiFi name', 'error');\n"
        "        return;\n"
        "    }\n"
        "\n"
        "    setButtonState(false);\n"
        "    showStatus('📡 Sending WiFi configuration...', 'connecting');\n"
        "    showProgress(true, 10);\n"
        "    pollCount = 0;\n"
        "\n"
        "    fetch('/api/wifi/config', {\n"
        "        method: 'POST',\n"
        "        headers: {'Content-Type': 'application/json'},\n"
        "        body: JSON.stringify({ssid: ssid, password: password})\n"
        "    })\n"
        "    .then(response => response.json())\n"
        "    .then(data => {\n"
        "        if (data.code === 200) {\n"
        "            showStatus('✓ Configuration sent. Connecting...', 'connecting');\n"
        "            showProgress(true, 20);\n"
        "            pollInterval = setInterval(pollWiFiStatus, 2000);\n"
        "            setTimeout(pollWiFiStatus, 500);\n"
        "        } else {\n"
        "            showProgress(false);\n"
        "            showStatus('✗ Configuration failed: ' + (data.message || 'Unknown error'), 'error');\n"
        "            setButtonState(true);\n"
        "        }\n"
        "    })\n"
        "    .catch(error => {\n"
        "        showProgress(false);\n"
        "        showStatus('✗ Network error: ' + error.message, 'error');\n"
        "        setButtonState(true);\n"
        "    });\n"
        "}\n"
        "\n"
        "// 页面加载完成后的初始化\n"
        "document.addEventListener('DOMContentLoaded', function() {\n"
        "    document.getElementById('ssid').focus();\n"
        "    document.getElementById('password').addEventListener('keypress', function(e) {\n"
        "        if (e.key === 'Enter') connectWiFi();\n"
        "    });\n"
        "    document.getElementById('ssid').addEventListener('keypress', function(e) {\n"
        "        if (e.key === 'Enter') document.getElementById('password').focus();\n"
        "    });\n"
        "});\n";

    httpd_resp_send_chunk(req, js_part3, strlen(js_part3));

    // 结束响应
    httpd_resp_send_chunk(req, NULL, 0);

    return ESP_OK;
}
