#!/usr/bin/env python3
"""
WiFi扫描功能测试脚本
专门测试wifi_scan_handler的修复效果
"""

import requests
import json
import time
import sys

DEVICE_IP = "************"
BASE_URL = f"http://{DEVICE_IP}"

def print_separator(title):
    """打印分隔符"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def check_device_connection():
    """检查设备连接"""
    try:
        response = requests.get(f"{BASE_URL}/api/device/info", timeout=3)
        if response.status_code == 200:
            print("✅ 设备连接正常")
            return True
        else:
            print(f"❌ 设备响应异常 (状态码: {response.status_code})")
            return False
    except Exception as e:
        print(f"❌ 无法连接到设备: {e}")
        print("请确认：")
        print("  1. 已连接到设备热点 WifiSpeaker-XXXXXX")
        print("  2. 设备IP地址正确 (************)")
        return False

def test_wifi_scan():
    """测试WiFi扫描功能"""
    print_separator("WiFi扫描功能测试")
    
    url = f"{BASE_URL}/api/wifi/scan"
    print(f"📡 请求URL: {url}")
    print(f"📡 请求方法: GET")
    
    try:
        print("\n🔄 开始WiFi扫描...")
        start_time = time.time()
        
        response = requests.get(url, timeout=15)  # 增加超时时间
        
        end_time = time.time()
        scan_duration = end_time - start_time
        
        print(f"⏱️  扫描耗时: {scan_duration:.2f} 秒")
        print(f"📥 响应状态码: {response.status_code}")
        print(f"📥 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"📥 响应数据结构: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                if data.get('code') == 200:
                    wifi_list = data.get('data', [])
                    print(f"\n✅ WiFi扫描成功!")
                    print(f"📊 扫描结果: 找到 {len(wifi_list)} 个WiFi网络")
                    
                    if len(wifi_list) > 0:
                        print(f"\n📋 WiFi网络列表:")
                        print(f"{'序号':<4} {'SSID':<20} {'信号强度':<10} {'加密类型':<15} {'认证模式'}")
                        print("-" * 70)
                        
                        for i, wifi in enumerate(wifi_list, 1):
                            ssid = wifi.get('ssid', 'Unknown')[:18]
                            rssi = wifi.get('rssi', 0)
                            auth_type = wifi.get('authType', 'Unknown')
                            auth_mode = wifi.get('authmode', -1)
                            
                            # 信号强度评级
                            if rssi > -50:
                                signal_level = "强"
                            elif rssi > -70:
                                signal_level = "中"
                            else:
                                signal_level = "弱"
                            
                            signal_str = f"{rssi}dBm({signal_level})"
                            
                            print(f"{i:<4} {ssid:<20} {signal_str:<10} {auth_type:<15} {auth_mode}")
                        
                        # 统计信息
                        print(f"\n📈 统计信息:")
                        auth_stats = {}
                        signal_stats = {"强": 0, "中": 0, "弱": 0}
                        
                        for wifi in wifi_list:
                            # 统计加密类型
                            auth_type = wifi.get('authType', 'Unknown')
                            auth_stats[auth_type] = auth_stats.get(auth_type, 0) + 1
                            
                            # 统计信号强度
                            rssi = wifi.get('rssi', 0)
                            if rssi > -50:
                                signal_stats["强"] += 1
                            elif rssi > -70:
                                signal_stats["中"] += 1
                            else:
                                signal_stats["弱"] += 1
                        
                        print(f"  加密类型分布: {auth_stats}")
                        print(f"  信号强度分布: {signal_stats}")
                        
                        # 推荐网络
                        open_networks = [w for w in wifi_list if w.get('authType') == 'Open']
                        strong_networks = [w for w in wifi_list if w.get('rssi', 0) > -50]
                        
                        if open_networks:
                            print(f"\n🔓 发现 {len(open_networks)} 个开放网络:")
                            for wifi in open_networks[:3]:
                                print(f"    - {wifi.get('ssid')} (信号: {wifi.get('rssi')}dBm)")
                        
                        if strong_networks:
                            print(f"\n📶 发现 {len(strong_networks)} 个强信号网络:")
                            for wifi in strong_networks[:3]:
                                print(f"    - {wifi.get('ssid')} (信号: {wifi.get('rssi')}dBm, 加密: {wifi.get('authType')})")
                        
                        return True
                    else:
                        print(f"\n⚠️  扫描结果为空")
                        print(f"可能的原因:")
                        print(f"  1. 附近没有WiFi网络")
                        print(f"  2. 设备WiFi扫描功能异常")
                        print(f"  3. 设备处于特殊模式")
                        return False
                else:
                    print(f"\n❌ API返回错误: {data.get('message', 'Unknown error')}")
                    return False
            except json.JSONDecodeError as e:
                print(f"\n❌ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
                return False
        else:
            print(f"\n❌ HTTP请求失败 (状态码: {response.status_code})")
            print(f"响应内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"\n⏰ 请求超时")
        print(f"WiFi扫描可能需要更长时间，请稍后重试")
        return False
    except Exception as e:
        print(f"\n❌ 请求异常: {e}")
        return False

def test_multiple_scans():
    """测试多次扫描"""
    print_separator("多次扫描测试")
    
    scan_count = 3
    results = []
    
    for i in range(scan_count):
        print(f"\n🔄 第 {i+1} 次扫描...")
        
        try:
            response = requests.get(f"{BASE_URL}/api/wifi/scan", timeout=15)
            if response.status_code == 200:
                data = response.json()
                if data.get('code') == 200:
                    wifi_count = len(data.get('data', []))
                    results.append(wifi_count)
                    print(f"  ✅ 扫描成功: 找到 {wifi_count} 个网络")
                else:
                    results.append(0)
                    print(f"  ❌ 扫描失败: {data.get('message')}")
            else:
                results.append(0)
                print(f"  ❌ HTTP错误: {response.status_code}")
        except Exception as e:
            results.append(0)
            print(f"  ❌ 异常: {e}")
        
        if i < scan_count - 1:
            print(f"  ⏳ 等待3秒后进行下次扫描...")
            time.sleep(3)
    
    print(f"\n📊 多次扫描结果:")
    print(f"  扫描次数: {scan_count}")
    print(f"  成功次数: {sum(1 for r in results if r > 0)}")
    print(f"  网络数量: {results}")
    print(f"  平均网络数: {sum(results) / len(results):.1f}")
    
    return sum(1 for r in results if r > 0) > 0

def main():
    """主函数"""
    print("🔍 WiFi扫描功能专项测试")
    print("📝 此脚本专门测试wifi_scan_handler的修复效果")
    
    # 1. 检查设备连接
    print_separator("设备连接检查")
    if not check_device_connection():
        return
    
    # 2. 单次扫描测试
    scan_success = test_wifi_scan()
    
    # 3. 多次扫描测试
    if scan_success:
        multi_scan_success = test_multiple_scans()
    else:
        print("\n⚠️  由于单次扫描失败，跳过多次扫描测试")
        multi_scan_success = False
    
    # 4. 总结
    print_separator("测试总结")
    
    if scan_success:
        print("✅ WiFi扫描功能正常")
        print("🔧 修复效果:")
        print("  • 添加了详细的错误处理")
        print("  • 增加了authType字段")
        print("  • 过滤了空SSID")
        print("  • 添加了详细的日志输出")
        
        if multi_scan_success:
            print("✅ 多次扫描稳定性良好")
        else:
            print("⚠️  多次扫描存在问题")
    else:
        print("❌ WiFi扫描功能异常")
        print("🔧 可能的问题:")
        print("  • 设备WiFi模块未正确初始化")
        print("  • APSTA模式下扫描限制")
        print("  • 硬件或驱动问题")
        print("  • 环境中确实没有WiFi网络")
    
    print("\n📚 相关文档:")
    print("  • API_Documentation.md - API接口文档")
    print("  • api_test_examples.py - 完整API测试")
    
    print("\n🛠️  调试建议:")
    print("  • 检查设备串口日志")
    print("  • 确认设备周围有WiFi网络")
    print("  • 尝试重启设备")

if __name__ == "__main__":
    main()
