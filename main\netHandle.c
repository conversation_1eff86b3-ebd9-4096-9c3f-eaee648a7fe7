#include "lwip/err.h"
#include "lwip/sys.h"
#include "lwip/inet.h"
#include "lwip/ip4_addr.h"
#include "lwip/netdb.h"
#include "lwip/sockets.h"
#include "esp_log.h"
#include "jsonAdvantage.h"
#include "sysconf.h"
#include "audio.h"
#include "ota_updater.h"
#include "netHandle.h"
#include "song_cache.h"

static const char *TAG = "netHandle";

static JsonWrapper jsonTaskDetails;

// 缓存相关全局变量
static cache_write_handle_t g_cache_write_handle = {0};
static cache_read_handle_t g_cache_read_handle = {0};
static bool g_is_playing_from_cache = false;
static bool g_is_caching_enabled = false;
static char g_current_song_md5[MAX_MD5_LEN] = {0};
static bool g_cache_write_active = false;

void mqtt_msg_handle(char *msg, int msgLen)
{
    JsonWrapper jsonRoot;
    if(!json_wrapper_init(msg, &jsonRoot))
    {
        goto ERROR;
    }
    const char *event = json_get_string(&jsonRoot, "event", NULL);
    if(!event) {
        goto ERROR;
    }

    JsonWrapper jsonData;
    if(!json_get_object(&jsonRoot, "data", &jsonData)) {
        goto ERROR;
    }
    if (strcmp(event, "dfota") == 0) {
        #if 0
        char *jsonStr = cJSON_Print(jsonData.json);
        ESP_LOGI(TAG,"jsonData=%s",jsonStr);
        free(jsonStr);
        #endif
        const char *url_json = json_get_string(&jsonData, "url1", NULL);
        const char *prjName = json_get_string(&jsonData, "prjName", NULL);
        const char *prjVer = json_get_string(&jsonData, "prjVer", NULL);
        
        if(!url_json || !prjName || !prjVer) {
            goto ERROR;
        }
        // 项目名称校验
        if (strcmp(prjName, PRJ_NAME) != 0) {
            goto ERROR;
        }
        // 版本校验
        if (strcmp(prjVer, PRJ_VER) <= 0) {
            goto ERROR;
        }
        //先停止播放
        cleanPlayTask(true);
        // 拿到OTA数据，执行ota升级
        start_ota_task(url_json);
    }
    else if (strcmp(event, "changeSet") == 0) {
        const char *vol = json_get_string(&jsonData, "vol", NULL);
        if(!vol) {
            goto ERROR;
        }
        g_system_volume = atoi(vol);
        save_basic_config();
    }
    else if (strcmp(event, "changeStatus") == 0) {
        const char *type = json_get_string(&jsonData, "type", NULL);
        if (type) {
            // 处理重启/关机逻辑...
            if(strcmp(type, "restart") == 0) {
                ESP_LOGI("TAG", "MQTT Control Restart...");
                System_Reboot(false);
            }
        }
    }
    else if (strcmp(event, "audioCaptureChange") == 0) {
        const char *ip = json_get_string(&jsonData, "ip", NULL);
        const char *port = json_get_string(&jsonData, "port", NULL);
        if(!ip ||!port) {
            goto ERROR;
        }
        sprintf(g_soundCard_server_Addr,"%s",ip);
        g_soundCard_server_port = atoi(port);
        save_basic_config();

        if(g_playTaskSrc==TASK_SRC_SOUNDCARD)
        {
            cleanPlayTask(true);
        }

        if (soundCard_tcp_socket >= 0)
        {
            close(soundCard_tcp_socket);
            soundCard_tcp_socket = -1;
        }
    }

    json_wrapper_free(&jsonRoot);
    return;
ERROR:
    ESP_LOGE(TAG, "Invalid message");
    json_wrapper_free(&jsonRoot);
    return;
}
unsigned int toatal_stream_len=0;

unsigned char tcp_msg_handle(unsigned char messageType, unsigned char *data, int dataLen, int jsonLen)
{
    JsonWrapper jsonRoot;
    if(!json_wrapper_init((char*)data, &jsonRoot))
    {
        goto ERROR;
    }
    
    if(messageType == TimeResponseMessage)
    {
        const char *strTime = json_get_string(&jsonRoot, "time", NULL);
        int tcpStatus = json_get_int(&jsonRoot, "tcpStatus", -1);
        if(!strTime || tcpStatus == -1) {
            goto ERROR;
        }
        sprintf(g_sys_timeStamp,"%s",strTime);
        #if 1
        if(tcpStatus == 0)
        {
            ESP_LOGI(TAG, "tcpStatus error,reconnect!");
            json_wrapper_free(&jsonRoot); 
            return TcpHandleResult_TcpError;
        }
        #endif
    }
    else if(messageType == ServerSendTaskRequestMessage)
    {
        ESP_LOGI(TAG,"new paly task!");
#if 0
        char *jsonStr = cJSON_Print(jsonRoot.json);
        ESP_LOGI(TAG,"jsonRoot=%s",jsonStr);
        free(jsonStr);
#endif
        //先停止播放
        cleanPlayTask(true);

        json_wrapper_copy(&jsonTaskDetails,&jsonRoot);

        g_playTaskSrc = json_get_int(&jsonRoot, "src", -1);
        const char *taskId = json_get_string(&jsonRoot, "taskId", NULL);

        g_playTaskId=atoi(taskId);
        g_playIndex=0;

        if(IS_SERVER_PLAY_MP3_SRC)
        {
            g_audio_channel_num = json_get_specIndex_int(&jsonTaskDetails, "channels", g_playIndex, 2);
            g_audio_sample_rate = atoi(json_get_specIndex_string(&jsonTaskDetails, "sampleRates", g_playIndex, "44100"));

            //start_mp3Player_stream_task();
        }
        else if (g_playTaskSrc == TASK_SRC_PAGING)
        {
            g_audio_channel_num = 1;
            //g_stream_ready_play = true;
            start_g722Player_stream_task();
        }
    }
    else if(messageType == ServerSendInstructMessage) 
    {
        ESP_LOGI(TAG,"play control command!");

        int instruct = json_get_int(&jsonRoot, "instruct", -1);

        if(instruct == 0) //播放
        {
            ESP_LOGI(TAG,"tcp control command:play!");
            if(g_playTaskId == 0)
            {
                ESP_LOGI(TAG,"g_playTaskId is 0!");
                cleanPlayTask(true);
                goto ERROR;
            }
        }
        else if(instruct == 2  ) 
        {
            ESP_LOGI(TAG,"tcp control command:stop!");
            
            const char *stopTaskId = json_get_string(&jsonRoot, "taskId", NULL);
            if(g_playTaskId != 0 && g_playTaskId !=atoi(stopTaskId))
            {
                ESP_LOGI(TAG,"taskId is not equal:g_playTaskId=%d,stopTaskId=%d",g_playTaskId,atoi(stopTaskId));
                goto ERROR;
            }
            cleanPlayTask(true);
            goto SUCCEED;
        }
        else if(instruct == 3  )
        {
            ESP_LOGI(TAG,"tcp control command:next song!");
#if 0
        char *jsonStr = cJSON_Print(jsonRoot.json);
        ESP_LOGI(TAG,"jsonRoot=%s",jsonStr);
        free(jsonStr);
#endif

            const char *file = json_get_string(&jsonRoot, "file", NULL);
            if(!file) {
                g_playIndex = 0;
            }
            else {
                g_playIndex = atoi(file); 
            }
            ESP_LOGI(TAG,"current_play_file=%d",g_playIndex);

            g_audio_channel_num = json_get_specIndex_int(&jsonTaskDetails, "channels", g_playIndex, 2);
            g_audio_sample_rate = atoi(json_get_specIndex_string(&jsonTaskDetails, "sampleRates", g_playIndex, "44100"));
            g_song_total_frames = json_get_specIndex_int(&jsonTaskDetails, "frames", g_playIndex, 0);
            g_song_length = json_get_specIndex_int(&jsonTaskDetails, "flens", g_playIndex, 0);

            const char *md5 = json_get_specIndex_string(&jsonTaskDetails, "md5s", g_playIndex, NULL);
            if(md5)
            {
                ESP_LOGI(TAG,"md5=%s",md5);
                strncpy(g_current_song_md5, md5, sizeof(g_current_song_md5) - 1);
                g_current_song_md5[sizeof(g_current_song_md5) - 1] = '\0';
                
                // 检查是否存在缓存
                if (g_is_caching_enabled && song_cache_exists(md5)) {
                    ESP_LOGI(TAG, "Found cached song: %s, playing from cache", md5);
                    
                    // 从缓存获取歌曲信息
                    cache_file_header_t cache_header;
                    if (song_cache_get_file_info(md5, &cache_header) == ESP_OK) {
                        g_audio_channel_num = cache_header.channels;
                        g_audio_sample_rate = cache_header.sample_rate;
                        g_song_total_frames = cache_header.total_frames;
                        g_song_length = cache_header.song_length;
                    }
                    
                    int tmp_task_id = g_playTaskId;
                    int tmp_task_src = g_playTaskSrc;
                    cleanPlayTask(true);
                    g_playTaskId = tmp_task_id;
                    g_playTaskSrc = tmp_task_src;
                    
                    // 开始从缓存播放
                    g_is_playing_from_cache = true;
                    start_mp3Player_stream_task();
                    
                    // 开始读取缓存文件
                    if (song_cache_start_read(md5, &g_cache_read_handle) == ESP_OK) {
                        g_stream_ready_play = true;
                        ESP_LOGI(TAG, "Started playing from cache");
                    } else {
                        ESP_LOGE(TAG, "Failed to start reading from cache, fallback to network");
                        g_is_playing_from_cache = false;
                        sendRequestDataCmd(g_song_total_frames<AUDIO_STREAM_DOWNLOAD_NUM_ONCE?g_song_total_frames:AUDIO_STREAM_DOWNLOAD_NUM_ONCE,0);
                    }
                } else {
                    ESP_LOGI(TAG, "Cache not found or disabled, playing from network");
                    g_is_playing_from_cache = false;
                    
                    int tmp_task_id = g_playTaskId;
                    int tmp_task_src = g_playTaskSrc;
                    cleanPlayTask(true);
                    g_playTaskId = tmp_task_id;
                    g_playTaskSrc = tmp_task_src;
                    start_mp3Player_stream_task();
                    
                    // 如果启用缓存且有足够空间，开始缓存写入
                    if (g_is_caching_enabled && strlen(md5) > 0) {
                        uint64_t estimated_size = g_song_length + 50*1024; // 估计需要的空间
                        if (song_cache_cleanup_space(estimated_size) == ESP_OK) {
                            if (song_cache_start_write(md5, g_audio_sample_rate, g_audio_channel_num, 
                                                     g_song_total_frames, g_song_length, &g_cache_write_handle) == ESP_OK) {
                                g_cache_write_active = true;
                                ESP_LOGI(TAG, "Started caching song: %s", md5);
                            }
                        }
                    }
                    
                    sendRequestDataCmd(g_song_total_frames<AUDIO_STREAM_DOWNLOAD_NUM_ONCE?g_song_total_frames:AUDIO_STREAM_DOWNLOAD_NUM_ONCE,0);
                }
            } else {
                // 没有MD5信息，按原来的方式处理
                int tmp_task_id = g_playTaskId;
                int tmp_task_src = g_playTaskSrc;
                cleanPlayTask(true);
                g_playTaskId = tmp_task_id;
                g_playTaskSrc = tmp_task_src;
                start_mp3Player_stream_task();
                sendRequestDataCmd(g_song_total_frames<AUDIO_STREAM_DOWNLOAD_NUM_ONCE?g_song_total_frames:AUDIO_STREAM_DOWNLOAD_NUM_ONCE,0);
            }
        }
    }
    else if (messageType == FileTransferMessage)
    {
        const char *strem_taskId = json_get_string(&jsonRoot, "taskId", NULL);
        if(!g_playTaskId || g_playTaskId !=atoi(strem_taskId))
        {
            ESP_LOGI(TAG,"taskId is not equal:g_playTaskId=%d,stopTaskId=%d",g_playTaskId,atoi(strem_taskId));
            goto ERROR;
        }

        #if 0
        char *jsonStr = cJSON_Print(jsonRoot.json);
        ESP_LOGI(TAG,"jsonRoot=%s",jsonStr);
        free(jsonStr);
        #endif
        unsigned int trTp = json_get_int(&jsonRoot, "trTp", 0);

        if (trTp == 2)
        {
            JsonWrapper jsonTrObj;
            if(!json_get_object(&jsonRoot, "trObj", &jsonTrObj)) {
                goto ERROR;
            }
            unsigned int nBeg = json_get_int(&jsonTrObj, "beg", 0);
            
            if(g_playTaskSrc!=TASK_SRC_PAGING)
            {
                if(nBeg>g_need_download_pos || nBeg == 0)
                {
                    ESP_LOGI(TAG,"file error,nBeg=%d,g_need_download_pos=%d",nBeg,g_need_download_pos);
                    goto ERROR;
                }
            }

            int streamLen = dataLen - jsonLen;

            toatal_stream_len+=streamLen;
            //ESP_LOGI(TAG, "g_has_download_pos1=%d",g_has_download_pos);
            rb_write(audio_data_rb,(char*)(data+jsonLen),streamLen,0);
            
            // 如果正在缓存写入，将数据写入缓存文件
            if (g_cache_write_active && g_cache_write_handle.file) {
                if (song_cache_write_frame(&g_cache_write_handle, data + jsonLen, streamLen) != ESP_OK) {
                    ESP_LOGE(TAG, "Failed to write frame to cache, cancelling cache write");
                    song_cache_cancel_write(&g_cache_write_handle);
                    g_cache_write_active = false;
                }
            }
            
            g_has_download_pos=nBeg;
            //ESP_LOGI(TAG, "g_has_download_pos2=%d",g_has_download_pos);
            //ESP_LOGI(TAG, "111g_has_download_pos=%d,g_need_download_pos=%d",g_has_download_pos,g_need_download_pos);

            //上一次的下载位置和本次的下载位置相同，说明数据已经全部下载完成
            if(g_playTaskSrc!=TASK_SRC_PAGING && g_has_download_pos == g_need_download_pos)
            {   
                ESP_LOGI(TAG, "g_has_download_pos=%d",g_has_download_pos);
                can_get_song_stream_flag = true; //外部可以获取，因为TCP的获取任务已完成
                
                // 如果歌曲下载完成且正在缓存写入，完成缓存写入
                if (g_cache_write_active && g_has_download_pos == g_song_total_frames) {
                    if (song_cache_finish_write(&g_cache_write_handle) == ESP_OK) {
                        ESP_LOGI(TAG, "Successfully cached song: %s", g_current_song_md5);
                    } else {
                        ESP_LOGE(TAG, "Failed to finish caching song: %s", g_current_song_md5);
                    }
                    g_cache_write_active = false;
                }
                
                if(g_has_download_pos>=80 || g_has_download_pos == g_song_total_frames)
                {
                    g_stream_ready_play = true;
                    //ESP_LOGI(TAG, "g_stream_ready_play=%d",g_stream_ready_play);
                }
                else
                {
                    if(g_has_download_pos<g_song_total_frames)
                    {
                        int diff = g_song_total_frames-g_has_download_pos;
                        sendRequestDataCmd(diff<AUDIO_STREAM_DOWNLOAD_NUM_ONCE?diff:AUDIO_STREAM_DOWNLOAD_NUM_ONCE,0);
                    }
                }
            }
            else if(g_playTaskSrc==TASK_SRC_PAGING)
            {
                if(g_has_download_pos>=1)
                {
                    g_stream_ready_play = true;
                }
            }
        }
    }


SUCCEED:
    json_wrapper_free(&jsonRoot);
    return TcpHandleResult_Success;
ERROR:
    ESP_LOGE(TAG, "Invalid message");
    json_wrapper_free(&jsonRoot);
    return TcpHandleResult_Failed;
}




/**
 * 发送播放进度信息
 *
 * 进度信息包括当前播放的文件索引、已播放帧数以及任务ID。
 *
 * @param frames 当前播放的帧数，如果为0，则发送当前累积帧数。
 */
void send_play_progress(unsigned int frames)
{
    if (g_playTaskId == 0) 
        return;

    if(tcp_socket<0)
    {
        return;
    }

    cJSON *root = cJSON_CreateObject();

    char playIndex[10]={0};
    cJSON_AddStringToObject(root,"file",itoa(g_playIndex,playIndex,10));
    cJSON_AddNumberToObject(root,"p",frames);
    char taskId[10]={0};
    cJSON_AddStringToObject(root,"taskId",itoa(g_playTaskId,taskId,10));

    //将json对象转换为字符串
    char *progressJson = cJSON_Print(root);

    unsigned int jsonLen = strlen(progressJson);
    controlTcpHeader getServerTimeHeader = {htonl(0x01020304), 0x01, 0x01, 0x15, 0x00000000, 0xff, htonl(jsonLen), htonl(jsonLen)};

    char *progressMessage = (char *)malloc(sizeof(getServerTimeHeader) + jsonLen + 1);
    memset(progressMessage, '\0', sizeof(getServerTimeHeader) + jsonLen + 1);
    memcpy(progressMessage, (const char *)&getServerTimeHeader, sizeof(getServerTimeHeader));
    memcpy(progressMessage + sizeof(getServerTimeHeader), (const char *)progressJson, jsonLen);


    int senLen=send(tcp_socket,(unsigned char  *)progressMessage, sizeof(getServerTimeHeader) + jsonLen, 0);
    if (senLen < 0) {
        ESP_LOGI(TAG, "Send error: %s", strerror(errno));
    }

    ESP_LOGI(TAG, "send_play_progress: 0x%x", frames);

    if(progressJson)
        free(progressJson); 
    if(root)
        cJSON_Delete(root);
    free(progressMessage);
}


/**
 * 发送请求数据命令
 *
 * 该函数用于发送请求数据的命令到服务器。如果启用了本地歌曲播放，
 * 它将从本地文件中读取音频数据。
 *
 * @param batch 请求的数据批次数量。
 */
void sendRequestDataCmd(int batch,int pos)
{
    if(tcp_socket<0)
    {
        return;
    }
    ESP_LOGI(TAG, "sendRequestDataCmd,start=%d,end=%d",g_need_download_pos+1,batch+g_need_download_pos);

    for (int i = 0; i < batch; i++)
    {
        if (g_playTaskId == 0 || (g_need_download_pos+1) > g_song_total_frames )
            break;
        g_need_download_pos++;
        can_get_song_stream_flag = false;

        cJSON *root = cJSON_CreateObject();
        char taskId[10]={0};
        cJSON_AddStringToObject(root,"taskId",itoa(g_playTaskId,taskId,10));
        cJSON_AddNumberToObject(root,"trTp",1);

        cJSON *trObjJson = cJSON_CreateObject();
        char playIndex[10]={0};
        cJSON_AddStringToObject(trObjJson,"file",itoa(g_playIndex,playIndex,10));
        cJSON_AddNumberToObject(trObjJson,"rdPos",pos?pos:g_need_download_pos);
        cJSON_AddNumberToObject(trObjJson,"status",0);

        cJSON_AddItemToObject(root,"trObj",trObjJson);

        //将json对象转换为字符串
        char *progressJson = cJSON_Print(root);

        unsigned int jsonLen = strlen(progressJson);
        controlTcpHeader getServerTimeHeader = {htonl(0x01020304), 0x01, 0x01, 0x14, 0x00000000, 0xff, htonl(jsonLen), htonl(jsonLen)};

        char *progressMessage = (char *)malloc(sizeof(getServerTimeHeader) + jsonLen + 1);
        memset(progressMessage, '\0', sizeof(getServerTimeHeader) + jsonLen + 1);
        memcpy(progressMessage, (const char *)&getServerTimeHeader, sizeof(getServerTimeHeader));
        memcpy(progressMessage + sizeof(getServerTimeHeader), (const char *)progressJson, jsonLen);

        send(tcp_socket,(unsigned char  *)progressMessage, sizeof(getServerTimeHeader) + jsonLen, 0);

        if(progressJson)
            free(progressJson); 
        if(root)
            cJSON_Delete(root);
        free(progressMessage);
    }
}



unsigned char soundCard_tcp_handle(unsigned short command, unsigned char *data, int dataLen)
{
    switch(command)
    {
        case CMD_SOUNDCARD_SERVER_EVENT:    //声卡采集事件
        {
            int audioEvent = data[0];
            ESP_LOGI(TAG, "soundCard_tcp_handle:audioEvent=%d",audioEvent);
            if(audioEvent == 1)
            {  
                if(g_playTaskSrc!=TASK_SRC_NONE)
                {
                    ESP_LOGI(TAG, "g_playTaskSrc!=TASK_SRC_NONE,return");
                    break;
                }
                int audioRate = (data[1]<<8) + data[2];
                int audioChannel = data[3];
                int audioBits = data[4];
                int audioCodes = data[5];
                int audioType = data[6];
                ESP_LOGI(TAG, "audioRate=%d,audioChannel=%d,audioBits=%d,audioCodes=%d,audioType=%d",audioRate,audioChannel,audioBits,audioCodes,audioType);
                cleanPlayTask(true);
                g_playTaskSrc = TASK_SRC_SOUNDCARD;
                start_opus_stream_task();
            }
            else
            {
                if(g_playTaskSrc==TASK_SRC_SOUNDCARD)
                {
                    cleanPlayTask(true);
                }
            }
        }
        break;
        case CMD_SOUNDCARD_SERVER_STREAM:    //声卡音频流
            if(g_playTaskSrc!=TASK_SRC_SOUNDCARD)
            {
                //ESP_LOGI(TAG, "g_playTaskSrc!=TASK_SRC_SOUNDCARD");
                break;
            }
            soundCard_stream_cnt++;
            int audioType = data[0];
            int audioLen = (data[1]<<8) + data[2];
            //ESP_LOGI(TAG, "audioType=%d,audioLen=%d",audioType,audioLen);
            esp_err_t err=ringbuf_write((char *)(data+3), audioLen, 0);
            if(err!=ESP_OK)
            {
                ESP_LOGE(TAG, "ringbuf_write failed");
                break;
            }
            g_has_download_pos++;
            if(g_has_download_pos>=65)
            {
                g_stream_ready_play = true;
            }
        break;
        case CMD_SOUNDCARD_CLIENT_HEARTBEAT:
            ESP_LOGI(TAG, "CMD_SOUNDCARD_CLIENT_HEARTBEAT...");
        break;
    }
    return 0;
}

/**
 * 从缓存读取音频帧数据
 * 供音频播放模块调用
 */
int song_cache_read_audio_frame(uint8_t *buffer, uint32_t buffer_size, uint32_t *frame_size)
{
    if (!g_is_playing_from_cache || !g_cache_read_handle.file) {
        return -1;  // 不是从缓存播放
    }
    
    esp_err_t ret = song_cache_read_frame(&g_cache_read_handle, buffer, frame_size);
    if (ret == ESP_ERR_NOT_FOUND) {
        // 缓存文件读取完成
        song_cache_finish_read(&g_cache_read_handle);
        g_is_playing_from_cache = false;
        ESP_LOGI(TAG, "Finished reading from cache");
        return 0;  // 播放完成
    } else if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Error reading from cache");
        song_cache_finish_read(&g_cache_read_handle);
        g_is_playing_from_cache = false;
        return -1;  // 读取错误
    }
    
    return 1;  // 成功读取
}

/**
 * 检查是否正在从缓存播放
 */
bool is_playing_from_cache(void)
{
    return g_is_playing_from_cache;
}

/**
 * 启用或禁用缓存功能
 */
void set_cache_enabled(bool enabled)
{
    g_is_caching_enabled = enabled;
    ESP_LOGI(TAG, "Cache %s", enabled ? "enabled" : "disabled");
}

/**
 * 获取缓存状态信息
 */
void get_cache_status(bool *enabled, uint64_t *used_space, uint64_t *free_space, uint32_t *file_count)
{
    if (enabled) *enabled = g_is_caching_enabled;
    if (used_space) *used_space = song_cache_get_used_space();
    if (free_space) *free_space = song_cache_get_free_space();
    // file_count 需要从缓存管理器获取，这里暂时设为0
    if (file_count) *file_count = 0;
}

/**
 * 清理播放任务时的缓存处理
 */
void cleanup_cache_on_task_end(void)
{
    // 如果正在缓存写入，取消写入
    if (g_cache_write_active) {
        song_cache_cancel_write(&g_cache_write_handle);
        g_cache_write_active = false;
        ESP_LOGI(TAG, "Cancelled cache write due to task cleanup");
    }
    
    // 如果正在从缓存读取，结束读取
    if (g_is_playing_from_cache && g_cache_read_handle.file) {
        song_cache_finish_read(&g_cache_read_handle);
        g_is_playing_from_cache = false;
        ESP_LOGI(TAG, "Finished cache read due to task cleanup");
    }
    
    // 清空当前歌曲MD5
    memset(g_current_song_md5, 0, sizeof(g_current_song_md5));
}