#ifndef _SOFTAP_CONFIG_H
#define _SOFTAP_CONFIG_H

#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_http_server.h"

// SoftAP配置参数
#define SOFTAP_SSID_PREFIX      "WifiSpeaker-"
#define SOFTAP_PASSWORD         "wifi87654321"
#define SOFTAP_MAX_STA_CONN     4
#define SOFTAP_CHANNEL          1
#define SOFTAP_IP_ADDR          "************"
#define SOFTAP_GATEWAY          "************"
#define SOFTAP_NETMASK          "*************"

// HTTP服务器配置
#define HTTP_SERVER_PORT        80
#define MAX_HTTP_RECV_BUFFER    1024
#define MAX_HTTP_SEND_BUFFER    2048

// API路径定义
#define API_ROOT                "/"
#define API_DEVICE_INFO         "/api/device/info"
#define API_WIFI_CONFIG         "/api/wifi/config"
#define API_WIFI_SCAN           "/api/wifi/scan"
#define API_WIFI_STATUS         "/api/wifi/status"
#define API_SYSTEM_RESET        "/api/system/reset"

// WiFi配置状态
typedef enum {
    WIFI_CONFIG_IDLE = 0,
    WIFI_CONFIG_CONNECTING,
    WIFI_CONFIG_CONNECTED,
    WIFI_CONFIG_FAILED
} wifi_config_status_t;

// WiFi扫描结果结构
typedef struct {
    char ssid[33];
    int8_t rssi;
    wifi_auth_mode_t authmode;
} wifi_scan_result_t;

// 设备信息结构
typedef struct {
    char device_code[64];
    char device_name[32];
    char firmware_version[32];
    char project_version[32];
    char mac_address[18];
    bool wifi_connected;
    char current_ssid[33];
    int8_t rssi;
} device_info_t;

// WiFi配置请求结构
typedef struct {
    char ssid[33];
    char password[65];
} wifi_config_request_t;

// HTTP响应结构
typedef struct {
    int code;
    char message[128];
    void* data;
} http_response_t;

// 全局变量声明
extern httpd_handle_t g_softap_server;
extern wifi_config_status_t g_wifi_config_status;
extern char g_softap_ssid[128];
extern bool g_softap_started;

// 函数声明
esp_err_t softap_init(void);
esp_err_t softap_start(void);
esp_err_t softap_stop(void);
esp_err_t softap_deinit(void);
void softap_shutdown_on_wifi_connected(void);

esp_err_t http_server_start(void);
esp_err_t http_server_stop(void);

// HTTP处理函数声明
esp_err_t device_info_handler(httpd_req_t *req);
esp_err_t wifi_config_handler(httpd_req_t *req);
esp_err_t wifi_scan_handler(httpd_req_t *req);
esp_err_t wifi_status_handler(httpd_req_t *req);
esp_err_t system_reset_handler(httpd_req_t *req);
esp_err_t favicon_handler(httpd_req_t *req);

// 工具函数声明
esp_err_t get_device_info(device_info_t *info);
esp_err_t scan_wifi_networks(wifi_scan_result_t *results, uint16_t *count);
esp_err_t configure_wifi_sta(const char *ssid, const char *password);
void send_json_response(httpd_req_t *req, int code, const char *message, const char *data);
void send_error_response(httpd_req_t *req, int code, const char *message);

// 外部函数声明（在main.c中实现）
void set_pending_wifi_config(const char* ssid, const char* password);

// 事件处理函数声明
void softap_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data);

#endif // _SOFTAP_CONFIG_H
