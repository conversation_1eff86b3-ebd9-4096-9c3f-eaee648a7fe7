#ifndef _SYS_CONFIG_H
#define _SYS_CONFIG_H

#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include "const.h"
#include "sysMethod.h"
#include "network.h"
#include "netHandle.h"
#include "audio.h"
#include "usb_rndis_4g_module.h"

typedef enum {
    LED_OFF,
    LED_ON,
    SLOW_BLINK,
    FAST_BLINK,
    PLAY_BLINK
} led_mode_t;

extern char g_device_code[48];
extern char g_sys_timeStamp[16];
extern char mqtt_sub_topic_name[96];
extern char g_mqtt_server[64];
extern int g_mqtt_port;
extern char g_tcp_server[64];
extern int g_tcp_port;

extern unsigned char g_system_volume;

extern char g_soundCard_server_Addr[64];    //声卡服务器地址
extern short int g_soundCard_server_port;         //声卡服务器端口

extern bool s_wifi_connected;

void save_basic_config();
void read_basic_config();

void GpioSetAmpValid(bool isValid);
void GPIO_OutPut_LedStatus(bool isValid);
void changeLedMode(led_mode_t mode);

void system_gpio_init();
void system_init(void);
void System_Reboot(bool bReset4G);


#define IS_SERVER_PLAY_MP3_SRC    (g_playTaskSrc == TASK_SRC_SERVER_MUSIC || g_playTaskSrc == TASK_SRC_CONTROLER_MUSIC || g_playTaskSrc == TASK_SRC_WEATHER || g_playTaskSrc == TASK_SRC_ALARM)

#endif