{"C_Cpp.intelliSenseEngine": "default", "idf.espIdfPathWin": "d:\\project\\esp32\\esp-idf\\v5.4\\esp-idf", "idf.openOcdConfigs": ["board/esp32s3-builtin.cfg"], "idf.portWin": "COM4", "idf.toolsPathWin": "d:\\project\\esp32\\esp-idf\\.espressif", "idf.customExtraVars": {"IDF_TARGET": "esp32s3"}, "files.associations": {"format": "cpp", "esp_wifi_types.h": "c", "event_groups.h": "c", "esp_http_client.h": "c", "string.h": "c", "esp_log.h": "c", "i2s.h": "c", "inttypes.h": "c", "stdarg.h": "c", "sdkconfig.h": "c", "task.h": "c", "audio.h": "c", "i2s_common.h": "c", "esp_netif.h": "c", "esp_peripherals.h": "c", "i2s_std.h": "c", "board.h": "c", "periph_wifi.h": "c", "gpio_sig_map.h": "c", "i2s_periph.h": "c", "compare": "c", "cstdint": "c", "sysmethod.h": "c", "sysconfig.h": "c", "esp_efuse.h": "c", "esp_mac.h": "c", "const.h": "c", "sysconf.h": "c", "stdio.h": "c", "stdbool.h": "c", "ip4_addr.h": "c", "network.h": "c", "sockets.h": "c", "cjson.h": "c", "array": "c", "atomic": "c", "bit": "c", "*.tcc": "c", "cctype": "c", "charconv": "c", "chrono": "c", "cinttypes": "c", "clocale": "c", "cmath": "c", "complex": "c", "concepts": "c", "condition_variable": "c", "cstdarg": "c", "cstddef": "c", "cstdio": "c", "cstdlib": "c", "cstring": "c", "ctime": "c", "cwchar": "c", "cwctype": "c", "deque": "c", "list": "c", "map": "c", "set": "c", "string": "c", "unordered_map": "c", "unordered_set": "c", "vector": "c", "exception": "c", "algorithm": "c", "functional": "c", "iterator": "c", "memory": "c", "memory_resource": "c", "numeric": "c", "optional": "c", "random": "c", "ratio": "c", "string_view": "c", "system_error": "c", "tuple": "c", "type_traits": "c", "utility": "c", "fstream": "c", "future": "c", "initializer_list": "c", "iomanip": "c", "iosfwd": "c", "iostream": "c", "istream": "c", "limits": "c", "mutex": "c", "new": "c", "numbers": "c", "ostream": "c", "semaphore": "c", "span": "c", "sstream": "c", "stdexcept": "c", "stop_token": "c", "streambuf": "c", "text_encoding": "c", "thread": "c", "typeinfo": "c", "variant": "c", "md5.h": "c", "stdint.h": "c", "mqtt_client.h": "c", "netdb.h": "c", "inet.h": "c", "err.h": "c", "nvs.h": "c", "gpio.h": "c", "esp_smartconfig.h": "c", "stdlib.h": "c", "nethandle.h": "c", "buffer": "c", "internet": "c", "socket": "c", "jsonadvantage.h": "c", "audio_event_iface.h": "c", "mp3_decoder.h": "c", "esp_err.h": "c", "*.inc": "c", "auto_mp3_dec.h": "c", "esp_compiler.h": "c", "ringbuf.h": "c", "semphr.h": "c", "audio_error.h": "c", "audio_mutex.h": "c", "audio_mem.h": "c", "audio_pipeline.h": "c", "i2s_stream.h": "c", "esp_alc.h": "c", "board_pins_config.h": "c", "g722_decoder.h": "c", "esp_https_ota.h": "c", "ota_updater.h": "c", "esp_system.h": "c", "uart.h": "c", "esp_timer.h": "c", "usb_types_ch9.h": "c", "usb_host.h": "c", "iot_usbh_cdc.h": "c", "iot_eth.h": "c", "song_cache.h": "c"}, "idf.flashType": "UART"}