#include "esp_log.h"
#include "stdlib.h"
#include "string.h"
#include "stdbool.h"
#include "esp_log.h"
#include "esp_wifi.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "sysconf.h"
#include "uart.h"


static const char *TAG = "UART";

#define UART_NUM UART_NUM_0
// 协议定义
#define START_CODE_1 0xAA
#define START_CODE_2 0x66
#define MAX_DATA_LEN 256-6  // 根据实际需求调整
#define BUF_SIZE (256)
#define RD_BUF_SIZE (BUF_SIZE)
#define WR_BUF_SIZE (BUF_SIZE)


#define UART_COMMAND_ONLINE 0x50 // 在线命令
#define UART_COMMAND_WIFI_CONNECT 0x51 // 测试WIFI连接

esp_err_t esp_smartconfig_stop(void);

typedef struct {
    uint8_t command;
    uint16_t data_len;
    uint8_t *data;
    uint8_t checksum;
} uart_frame_t;

static void uart_init() {
    // UART 配置参数
    uart_config_t uart_config = {
        .baud_rate = 115200,
        .data_bits = UART_DATA_8_BITS,
        .parity = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };
    
    // 安装 UART 驱动程序
    uart_driver_install(UART_NUM, RD_BUF_SIZE, WR_BUF_SIZE, 0, NULL, 0);
    uart_param_config(UART_NUM, &uart_config);
    uart_set_pin(UART_NUM, GPIO_NUM_21, GPIO_NUM_20, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);
}

// 计算校验和
static uint8_t calculate_checksum(const uart_frame_t *frame) {
    uint8_t sum = frame->command;
    
    // 添加数据长度 (小端)
    sum += (frame->data_len & 0xFF);
    sum += ((frame->data_len >> 8) & 0xFF);
    
    // 添加数据域
    for (int i = 0; i < frame->data_len; i++) {
        sum += frame->data[i];
    }
    
    return sum;
}


void send_uart_frame(uint8_t command, const uint8_t *data, uint16_t data_len) {
    uint8_t frame[6 + data_len];  // 起始码2 + 命令1 + 长度2 + 校验1 + 数据
    uint8_t checksum = 0;
    
    // 起始码
    frame[0] = START_CODE_1;
    frame[1] = START_CODE_2;
    
    // 命令字
    frame[2] = command;
    checksum += command;
    
    // 数据长度 (小端)
    frame[3] = data_len & 0xFF;
    frame[4] = (data_len >> 8) & 0xFF;
    checksum += frame[3];
    checksum += frame[4];
    
    // 数据域
    if (data_len > 0) {
        memcpy(&frame[5], data, data_len);
        for (int i = 0; i < data_len; i++) {
            checksum += data[i];
        }
    }
    
    // 校验和
    frame[5 + data_len] = checksum;
    
    // 发送帧
    uart_write_bytes(UART_NUM, (const char *)frame, 6 + data_len);
}

void SendUart_CMD_Online()
{
    uint8_t command = UART_COMMAND_ONLINE;
    uint8_t *data = (uint8_t *)malloc(WR_BUF_SIZE);
    uint16_t data_len = 0;
    data[data_len++] = strlen(g_device_code);
    memcpy(&data[data_len], g_device_code, strlen(g_device_code));
    data_len += strlen(g_device_code);
    data[data_len++] = strlen(PRJ_VER);
    memcpy(&data[data_len], PRJ_VER, strlen(PRJ_VER));
    data_len += strlen(PRJ_VER);
    //加入WIFI连接状态
    data[data_len++] = s_wifi_connected; 
    send_uart_frame(command, data, data_len);
    free(data);
}



// 处理接收到的有效帧
static void process_frame(const uart_frame_t *frame) {
    ESP_LOGI(TAG, "Received valid frame: CMD=0x%02X, Len=%d", frame->command, frame->data_len);
    
    // 这里添加您的具体命令处理逻辑
    switch(frame->command) {
        case UART_COMMAND_ONLINE:
            SendUart_CMD_Online();
            break;
        case UART_COMMAND_WIFI_CONNECT:
        {
            char ssid[32] = {0};
            char password[32] = {0};
            int ssid_len = frame->data[0];
            memcpy(ssid, &frame->data[1], ssid_len);
            ESP_LOGI(TAG, "ssid: %s", ssid);
            int password_len = frame->data[ssid_len+1];
            memcpy(password, &frame->data[ssid_len+2], password_len);
            ESP_LOGI(TAG, "password: %s", password);

            wifi_config_t wifi_config = {0};
            bzero(&wifi_config, sizeof(wifi_config_t));
            strcpy((char *)wifi_config.sta.ssid, ssid);
            strcpy((char *)wifi_config.sta.password, password);
            wifi_config.sta.bssid_set = false;
            esp_smartconfig_stop();
            esp_wifi_disconnect();
            esp_wifi_set_config(WIFI_IF_STA, &wifi_config);
            esp_wifi_connect();
        }
        break;
        default:
            ESP_LOGW(TAG, "Unknown command: 0x%02X", frame->command);
            break;
    }
    #if 0
    // 打印数据内容（调试用）
    if (frame->data_len > 0) {
        ESP_LOG_BUFFER_HEXDUMP(TAG, frame->data, frame->data_len, ESP_LOG_INFO);
    }
    #endif
}

// 帧解析状态机
static void uart_event_task(void *pvParameters) {
    uint8_t data[RD_BUF_SIZE]={0};
    uart_frame_t current_frame = {0};
    uint8_t *frame_buffer = NULL;
    uint16_t bytes_received = 0;
    
    enum {
        WAIT_START1,
        WAIT_START2,
        WAIT_COMMAND,
        WAIT_LEN_LOW,
        WAIT_LEN_HIGH,
        WAIT_DATA,
        WAIT_CHECKSUM
    } state = WAIT_START1;

    //上电第一次发送在线信息
    SendUart_CMD_Online();
    
    while (1) {
        int len = uart_read_bytes(UART_NUM, data, RD_BUF_SIZE, pdMS_TO_TICKS(50));
        if (len > 0) {
            for (int i = 0; i < len; i++) {
                uint8_t byte = data[i];
                
                switch (state) {
                    case WAIT_START1:
                        if (byte == START_CODE_1) {
                            state = WAIT_START2;
                        }
                        break;
                        
                    case WAIT_START2:
                        if (byte == START_CODE_2) {
                            state = WAIT_COMMAND;
                            bytes_received = 0;
                        } else {
                            state = WAIT_START1;
                        }
                        break;
                        
                    case WAIT_COMMAND:
                        current_frame.command = byte;
                        state = WAIT_LEN_LOW;
                        break;
                        
                    case WAIT_LEN_LOW:
                        current_frame.data_len = byte;
                        state = WAIT_LEN_HIGH;
                        break;
                        
                    case WAIT_LEN_HIGH:
                        current_frame.data_len |= (byte << 8);
                        
                        // 检查数据长度是否合理
                        if (current_frame.data_len > BUF_SIZE) {
                            ESP_LOGE(TAG, "Invalid data length: %d", current_frame.data_len);
                            state = WAIT_START1;
                            break;
                        }
                        
                        // 分配内存存储数据
                        if (frame_buffer) free(frame_buffer);
                        frame_buffer = (uint8_t *)malloc(current_frame.data_len);
                        current_frame.data = frame_buffer;
                        
                        if (current_frame.data_len > 0) {
                            state = WAIT_DATA;
                        } else {
                            state = WAIT_CHECKSUM;
                        }
                        break;
                        
                    case WAIT_DATA:
                        current_frame.data[bytes_received++] = byte;
                        if (bytes_received >= current_frame.data_len) {
                            state = WAIT_CHECKSUM;
                        }
                        break;
                        
                    case WAIT_CHECKSUM:
                        current_frame.checksum = byte;
                        
                        // 验证校验和
                        uint8_t calculated_checksum = calculate_checksum(&current_frame);
                        if (calculated_checksum == current_frame.checksum) {
                            process_frame(&current_frame);
                        } else {
                            //ESP_LOGE(TAG, "Checksum error! Received: 0x%02X, Calculated: 0x%02X", 
                                     //current_frame.checksum, calculated_checksum);
                            ESP_LOGE(TAG, "Checksum error");
                        }
                        
                        // 重置状态机
                        state = WAIT_START1;
                        if (frame_buffer) {
                            free(frame_buffer);
                            frame_buffer = NULL;
                        }
                        break;
                }
            }
        }
    }
    
    if (frame_buffer) free(frame_buffer);
    vTaskDelete(NULL);
}


void uart_task_create(void) {
    uart_init();
    xTaskCreate(uart_event_task, "uart", 2560, NULL, 5, NULL);
}