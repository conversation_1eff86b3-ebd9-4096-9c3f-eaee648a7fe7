#!/usr/bin/env python3
"""
测试SoftAP自动关闭功能
验证WiFi配网成功后SoftAP是否正确关闭
"""

import requests
import json
import time
import subprocess
import sys

DEVICE_IP = "************"
BASE_URL = f"http://{DEVICE_IP}"

def check_device_reachable():
    """检查设备是否可达"""
    try:
        response = requests.get(f"{BASE_URL}/api/device/info", timeout=3)
        return response.status_code == 200
    except:
        return False

def get_wifi_status():
    """获取WiFi状态"""
    try:
        response = requests.get(f"{BASE_URL}/api/wifi/status", timeout=3)
        if response.status_code == 200:
            return response.json().get('data', {})
        return None
    except:
        return None

def configure_wifi(ssid, password):
    """配置WiFi"""
    try:
        data = {"ssid": ssid, "password": password}
        response = requests.post(
            f"{BASE_URL}/api/wifi/config", 
            json=data, 
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        return response.status_code == 200
    except:
        return False

def scan_wifi_networks():
    """扫描可用的WiFi网络"""
    try:
        # 使用系统命令扫描WiFi（Windows）
        if sys.platform == "win32":
            result = subprocess.run(
                ["netsh", "wlan", "show", "profiles"], 
                capture_output=True, 
                text=True
            )
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                networks = []
                for line in lines:
                    if "All User Profile" in line:
                        network = line.split(':')[1].strip()
                        networks.append(network)
                return networks[:5]  # 返回前5个
        return []
    except:
        return []

def test_softap_shutdown():
    """测试SoftAP自动关闭功能"""
    print("=" * 60)
    print("SoftAP自动关闭功能测试")
    print("=" * 60)
    
    # 1. 检查初始连接
    print("1. 检查设备连接...")
    if not check_device_reachable():
        print("✗ 无法连接到设备，请确认：")
        print("  - 已连接到设备热点 WifiSpeaker-XXXXXX")
        print("  - 设备IP地址正确 (************)")
        return False
    print("✓ 设备连接正常")
    
    # 2. 获取初始WiFi状态
    print("\n2. 获取初始WiFi状态...")
    initial_status = get_wifi_status()
    if initial_status:
        print(f"✓ 当前状态: {initial_status.get('status', 'unknown')}")
        print(f"  已连接: {initial_status.get('connected', False)}")
    else:
        print("✗ 无法获取WiFi状态")
        return False
    
    # 3. 扫描可用网络
    print("\n3. 扫描可用WiFi网络...")
    networks = scan_wifi_networks()
    if networks:
        print("✓ 发现以下WiFi网络:")
        for i, network in enumerate(networks, 1):
            print(f"  {i}. {network}")
    else:
        print("! 未发现WiFi网络，将使用手动输入")
    
    # 4. 获取用户输入
    print("\n4. WiFi配置...")
    if networks:
        print("请选择要连接的WiFi网络:")
        for i, network in enumerate(networks, 1):
            print(f"  {i}. {network}")
        print("  0. 手动输入")
        
        try:
            choice = int(input("请输入选择 (0-{}): ".format(len(networks))))
            if 1 <= choice <= len(networks):
                ssid = networks[choice - 1]
            else:
                ssid = input("请输入WiFi名称: ").strip()
        except:
            ssid = input("请输入WiFi名称: ").strip()
    else:
        ssid = input("请输入WiFi名称: ").strip()
    
    if not ssid:
        print("✗ WiFi名称不能为空")
        return False
    
    password = input("请输入WiFi密码: ").strip()
    
    # 5. 配置WiFi
    print(f"\n5. 配置WiFi (SSID: {ssid})...")
    if not configure_wifi(ssid, password):
        print("✗ WiFi配置失败")
        return False
    print("✓ WiFi配置请求发送成功")
    
    # 6. 监控连接状态
    print("\n6. 监控WiFi连接状态...")
    max_wait_time = 30  # 最大等待30秒
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        status = get_wifi_status()
        if status:
            current_status = status.get('status', 'unknown')
            connected = status.get('connected', False)
            
            print(f"  状态: {current_status}, 已连接: {connected}")
            
            if connected:
                print("✓ WiFi连接成功!")
                break
            elif current_status == 'failed':
                print("✗ WiFi连接失败")
                return False
        else:
            print("  无法获取状态（可能SoftAP已关闭）")
            break
        
        time.sleep(2)
    
    # 7. 验证SoftAP是否关闭
    print("\n7. 验证SoftAP是否已关闭...")
    time.sleep(3)  # 等待一下确保关闭完成
    
    if check_device_reachable():
        print("! SoftAP仍然可达，可能未正确关闭")
        return False
    else:
        print("✓ SoftAP已正确关闭，无法再连接到配网热点")
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("✓ SoftAP自动关闭功能工作正常")
    print("设备现在应该已连接到您的WiFi网络")
    print("=" * 60)
    
    return True

def main():
    """主函数"""
    print("SoftAP自动关闭功能测试工具")
    print("此工具将测试WiFi配网成功后SoftAP是否正确关闭")
    print()
    
    input("请确认已连接到设备热点，然后按回车继续...")
    
    success = test_softap_shutdown()
    
    if success:
        print("\n建议后续操作:")
        print("1. 检查设备是否出现在路由器的设备列表中")
        print("2. 尝试通过路由器分配的IP地址访问设备")
        print("3. 验证设备的正常网络功能")
    else:
        print("\n测试失败，请检查:")
        print("1. WiFi名称和密码是否正确")
        print("2. WiFi网络是否正常工作")
        print("3. 设备是否支持该WiFi网络的加密方式")

if __name__ == "__main__":
    main()
