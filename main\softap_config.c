#include "softap_config.h"
#include "simple_page.h"
#include <sys/socket.h>
#include <arpa/inet.h>
#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_netif.h"
#include "esp_http_server.h"
#include "esp_mac.h"
#include "lwip/inet.h"
#include "cJSON.h"
#include "nvs.h"
#include "const.h"

static const char *TAG = "SoftAP_Config";

// 全局变量定义
httpd_handle_t g_softap_server = NULL;
wifi_config_status_t g_wifi_config_status = WIFI_CONFIG_IDLE;
char g_softap_ssid[128] = {0};
bool g_softap_started = false;

static esp_netif_t *g_softap_netif = NULL;
static esp_netif_t *g_sta_netif = NULL;

// 外部变量声明
extern bool s_wifi_connected;
extern wifi_config_t wifi_config;
extern char g_device_code[64];

// 优雅关闭SoftAP模式
static void graceful_shutdown_softap(void)
{
    if (!g_softap_started) {
        return;
    }

    ESP_LOGI(TAG, "Gracefully shutting down SoftAP mode");
    // 延时确保最后的HTTP响应发送完成
    vTaskDelay(1500 / portTICK_PERIOD_MS);

    // 停止HTTP服务器
    if (g_softap_server) {
        ESP_LOGI(TAG, "Stopping HTTP server");
        http_server_stop();
    }

    // 延时确保最后的HTTP响应发送完成
    vTaskDelay(1000 / portTICK_PERIOD_MS);

    // 停止SoftAP
    ESP_LOGI(TAG, "Stopping SoftAP");
    softap_stop();

    ESP_LOGI(TAG, "SoftAP shutdown completed");
}

// 清理SoftAP网络接口资源
static void cleanup_softap_netif(void)
{
    // 注意：在WiFi连接成功后，我们不应该销毁网络接口
    // 因为STA接口仍在使用中，只需要停止AP相关的服务
    ESP_LOGI(TAG, "SoftAP network interface cleanup completed");
}

// 公共函数：WiFi连接成功时关闭SoftAP
void softap_shutdown_on_wifi_connected(void)
{
    graceful_shutdown_softap();
    cleanup_softap_netif();
}

// 初始化SoftAP
esp_err_t softap_init(void)
{
    // 生成SSID（设备码后6位）
    snprintf(g_softap_ssid, sizeof(g_softap_ssid), "%s%s", 
            SOFTAP_SSID_PREFIX, &g_device_code[strlen(g_device_code) - 6]);
    
    
    ESP_LOGI(TAG, "SoftAP SSID: %s", g_softap_ssid);
    
    // 初始化网络接口
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    
    // 创建AP和STA网络接口
    g_softap_netif = esp_netif_create_default_wifi_ap();
    g_sta_netif = esp_netif_create_default_wifi_sta();
    
    // 配置AP的IP地址
    esp_netif_ip_info_t ip_info;
    inet_pton(AF_INET, SOFTAP_IP_ADDR, &ip_info.ip);
    inet_pton(AF_INET, SOFTAP_GATEWAY, &ip_info.gw);
    inet_pton(AF_INET, SOFTAP_NETMASK, &ip_info.netmask);
    
    ESP_ERROR_CHECK(esp_netif_dhcps_stop(g_softap_netif));
    ESP_ERROR_CHECK(esp_netif_set_ip_info(g_softap_netif, &ip_info));
    ESP_ERROR_CHECK(esp_netif_dhcps_start(g_softap_netif));
    
    // 初始化WiFi
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));
    
    return ESP_OK;
}

// 启动SoftAP
esp_err_t softap_start(void)
{
    if (g_softap_started) {
        return ESP_OK;
    }
    
    // 配置AP
    wifi_config_t ap_config = {
        .ap = {
            .ssid_len = strlen(g_softap_ssid),
            .channel = SOFTAP_CHANNEL,
            .password = SOFTAP_PASSWORD,
            .max_connection = SOFTAP_MAX_STA_CONN,
            .authmode = WIFI_AUTH_WPA2_PSK,
            .pmf_cfg = {
                .required = false,
            },
        },
    };
    strcpy((char*)ap_config.ap.ssid, g_softap_ssid);
    
    // 设置WiFi模式为APSTA
    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_APSTA));
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_AP, &ap_config));
    ESP_ERROR_CHECK(esp_wifi_start());
    
    g_softap_started = true;
    ESP_LOGI(TAG, "SoftAP started. SSID:%s password:%s", g_softap_ssid, SOFTAP_PASSWORD);
    
    return ESP_OK;
}

// 停止SoftAP
esp_err_t softap_stop(void)
{
    if (!g_softap_started) {
        return ESP_OK;
    }

    // 如果WiFi已连接，只需要切换到STA模式
    if (s_wifi_connected) {
        ESP_LOGI(TAG, "WiFi connected, switching from APSTA to STA mode");
        esp_err_t ret = esp_wifi_set_mode(WIFI_MODE_STA);
        if (ret != ESP_OK) {
            ESP_LOGW(TAG, "Failed to switch to STA mode: %s", esp_err_to_name(ret));
        }
    } else {
        // 如果WiFi未连接，完全停止WiFi
        ESP_LOGI(TAG, "WiFi not connected, stopping WiFi completely");
        esp_err_t ret = esp_wifi_stop();
        if (ret != ESP_OK) {
            ESP_LOGW(TAG, "Failed to stop WiFi: %s", esp_err_to_name(ret));
        }
    }

    g_softap_started = false;
    ESP_LOGI(TAG, "SoftAP stopped");

    return ESP_OK;
}

// 反初始化SoftAP
esp_err_t softap_deinit(void)
{
    if (g_softap_started) {
        softap_stop();
    }
    
    ESP_ERROR_CHECK(esp_wifi_deinit());
    
    if (g_softap_netif) {
        esp_netif_destroy(g_softap_netif);
        g_softap_netif = NULL;
    }
    
    if (g_sta_netif) {
        esp_netif_destroy(g_sta_netif);
        g_sta_netif = NULL;
    }
    
    return ESP_OK;
}

// 获取设备信息
esp_err_t get_device_info(device_info_t *info)
{
    if (!info) {
        return ESP_ERR_INVALID_ARG;
    }
    
    // 获取MAC地址
    uint8_t mac[6];
    esp_wifi_get_mac(WIFI_IF_STA, mac);
    snprintf(info->mac_address, sizeof(info->mac_address), 
             "%02x:%02x:%02x:%02x:%02x:%02x", 
             mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
    
    // 填充设备信息
    strncpy(info->device_code, g_device_code, sizeof(info->device_code) - 1);
    strncpy(info->device_name, PRJ_NAME, sizeof(info->device_name) - 1);
    strncpy(info->firmware_version, FIRMWARE_VERISON, sizeof(info->firmware_version) - 1);
    strncpy(info->project_version, PRJ_VER, sizeof(info->project_version) - 1);
    info->wifi_connected = s_wifi_connected;
    
    // 获取当前连接的WiFi信息
    if (s_wifi_connected) {
        wifi_ap_record_t ap_info;
        if (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK) {
            strncpy(info->current_ssid, (char*)ap_info.ssid, sizeof(info->current_ssid) - 1);
            info->rssi = ap_info.rssi;
        }
    }
    
    return ESP_OK;
}

// 配置WiFi STA模式
esp_err_t configure_wifi_sta(const char *ssid, const char *password)
{
    if (!ssid || strlen(ssid) == 0) {
        return ESP_ERR_INVALID_ARG;
    }
    
    g_wifi_config_status = WIFI_CONFIG_CONNECTING;

    // 设置待保存的WiFi配置（将在获取IP后保存）
    set_pending_wifi_config(ssid, password);

    // 配置STA
    wifi_config_t sta_config = {0};
    strncpy((char*)sta_config.sta.ssid, ssid, sizeof(sta_config.sta.ssid) - 1);
    if (password) {
        strncpy((char*)sta_config.sta.password, password, sizeof(sta_config.sta.password) - 1);
    }

    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &sta_config));
    ESP_ERROR_CHECK(esp_wifi_connect());

    ESP_LOGI(TAG, "Connecting to WiFi SSID: %s", ssid);
    
    return ESP_OK;
}

// 发送JSON响应
void send_json_response(httpd_req_t *req, int code, const char *message, const char *data)
{
    cJSON *json = cJSON_CreateObject();
    cJSON_AddNumberToObject(json, "code", code);
    cJSON_AddStringToObject(json, "message", message ? message : "");
    
    if (data) {
        cJSON *data_json = cJSON_Parse(data);
        if (data_json) {
            cJSON_AddItemToObject(json, "data", data_json);
        } else {
            cJSON_AddStringToObject(json, "data", data);
        }
    }
    
    char *json_string = cJSON_Print(json);
    
    httpd_resp_set_type(req, "application/json");
    httpd_resp_set_hdr(req, "Access-Control-Allow-Origin", "*");
    httpd_resp_send(req, json_string, strlen(json_string));
    
    free(json_string);
    cJSON_Delete(json);
}

// 发送错误响应
void send_error_response(httpd_req_t *req, int code, const char *message)
{
    send_json_response(req, code, message, NULL);
}



// 设备信息处理器
esp_err_t device_info_handler(httpd_req_t *req)
{
    device_info_t info = {0};
    esp_err_t ret = get_device_info(&info);

    if (ret != ESP_OK) {
        send_error_response(req, 500, "Failed to get device info");
        return ESP_OK;
    }

    cJSON *json = cJSON_CreateObject();
    cJSON_AddStringToObject(json, "deviceCode", info.device_code);
    cJSON_AddStringToObject(json, "deviceName", info.device_name);
    cJSON_AddStringToObject(json, "firmwareVersion", info.firmware_version);
    cJSON_AddStringToObject(json, "projectVersion", info.project_version);
    cJSON_AddStringToObject(json, "macAddress", info.mac_address);
    cJSON_AddBoolToObject(json, "wifiConnected", info.wifi_connected);
    cJSON_AddStringToObject(json, "currentSSID", info.current_ssid);
    cJSON_AddNumberToObject(json, "rssi", info.rssi);

    char *json_string = cJSON_Print(json);
    send_json_response(req, 200, "Success", json_string);

    free(json_string);
    cJSON_Delete(json);

    return ESP_OK;
}

// WiFi配置处理器
esp_err_t wifi_config_handler(httpd_req_t *req)
{
    char *content = malloc(MAX_HTTP_RECV_BUFFER);
    if (!content) {
        send_error_response(req, 500, "Memory allocation failed");
        return ESP_OK;
    }

    int ret = httpd_req_recv(req, content, MAX_HTTP_RECV_BUFFER - 1);

    if (ret <= 0) {
        send_error_response(req, 400, "Invalid request data");
        free(content);
        return ESP_OK;
    }

    content[ret] = '\0';
    ESP_LOGI(TAG, "Received WiFi config: %.*s", ret > 100 ? 100 : ret, content);

    cJSON *json = cJSON_Parse(content);
    if (!json) {
        send_error_response(req, 400, "Invalid JSON format");
        free(content);
        return ESP_OK;
    }

    cJSON *ssid_json = cJSON_GetObjectItem(json, "ssid");
    cJSON *password_json = cJSON_GetObjectItem(json, "password");

    if (!cJSON_IsString(ssid_json) || strlen(ssid_json->valuestring) == 0) {
        send_error_response(req, 400, "Invalid SSID");
        cJSON_Delete(json);
        free(content);
        return ESP_OK;
    }

    const char *ssid = ssid_json->valuestring;
    const char *password = cJSON_IsString(password_json) ? password_json->valuestring : "";

    esp_err_t config_ret = configure_wifi_sta(ssid, password);
    if (config_ret != ESP_OK) {
        send_error_response(req, 500, "Failed to configure WiFi");
    } else {
        send_json_response(req, 200, "WiFi configuration started", NULL);
        ESP_LOGI(TAG, "WiFi configuration will be saved after successful connection");
    }

    cJSON_Delete(json);
    free(content);
    return ESP_OK;
}

// WiFi状态处理器
esp_err_t wifi_status_handler(httpd_req_t *req)
{
    cJSON *json = cJSON_CreateObject();

    const char *status_str;
    switch (g_wifi_config_status) {
        case WIFI_CONFIG_IDLE:
            status_str = "idle";
            break;
        case WIFI_CONFIG_CONNECTING:
            status_str = "connecting";
            break;
        case WIFI_CONFIG_CONNECTED:
            status_str = "connected";
            break;
        case WIFI_CONFIG_FAILED:
            status_str = "failed";
            break;
        default:
            status_str = "unknown";
            break;
    }

    cJSON_AddStringToObject(json, "status", status_str);
    cJSON_AddBoolToObject(json, "connected", s_wifi_connected);

    if (s_wifi_connected) {
        wifi_ap_record_t ap_info;
        if (esp_wifi_sta_get_ap_info(&ap_info) == ESP_OK) {
            cJSON_AddStringToObject(json, "ssid", (char*)ap_info.ssid);
            cJSON_AddNumberToObject(json, "rssi", ap_info.rssi);
        }
    }

    char *json_string = cJSON_Print(json);
    send_json_response(req, 200, "Success", json_string);

    free(json_string);
    cJSON_Delete(json);

    return ESP_OK;
}

// 获取认证类型字符串
static const char* get_auth_type_string(wifi_auth_mode_t authmode)
{
    switch (authmode) {
        case WIFI_AUTH_OPEN:
            return "Open";
        case WIFI_AUTH_WEP:
            return "WEP";
        case WIFI_AUTH_WPA_PSK:
            return "WPA PSK";
        case WIFI_AUTH_WPA2_PSK:
            return "WPA2 PSK";
        case WIFI_AUTH_WPA_WPA2_PSK:
            return "WPA/WPA2 PSK";
        case WIFI_AUTH_WPA2_ENTERPRISE:
            return "WPA2 Enterprise";
        case WIFI_AUTH_WPA3_PSK:
            return "WPA3 PSK";
        case WIFI_AUTH_WPA2_WPA3_PSK:
            return "WPA2/WPA3 PSK";
        default:
            return "Unknown";
    }
}

// WiFi扫描处理器
esp_err_t wifi_scan_handler(httpd_req_t *req)
{
    ESP_LOGI(TAG, "WiFi scan request received");

    uint16_t number = 15;
    wifi_ap_record_t ap_info[15];
    uint16_t ap_count = 0;

    // 启动WiFi扫描
    esp_err_t scan_ret = esp_wifi_scan_start(NULL, true);
    if (scan_ret != ESP_OK) {
        ESP_LOGE(TAG, "WiFi scan start failed: %s", esp_err_to_name(scan_ret));
        send_error_response(req, 500, "WiFi scan start failed");
        return ESP_OK;
    }

    // 获取扫描结果
    esp_err_t get_records_ret = esp_wifi_scan_get_ap_records(&number, ap_info);
    esp_err_t get_num_ret = esp_wifi_scan_get_ap_num(&ap_count);

    if (get_records_ret != ESP_OK || get_num_ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get scan results: records=%s, num=%s",
                 esp_err_to_name(get_records_ret), esp_err_to_name(get_num_ret));
        send_error_response(req, 500, "Failed to get scan results");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "WiFi scan completed: found %d networks, retrieved %d records", ap_count, number);

    cJSON *json_array = cJSON_CreateArray();

    // 处理扫描结果
    for (int i = 0; i < number && i<sizeof(ap_info); i++) {
        // 过滤掉空SSID
        if (strlen((char*)ap_info[i].ssid) == 0) {
            continue;
        }

        cJSON *ap_json = cJSON_CreateObject();
        cJSON_AddStringToObject(ap_json, "ssid", (char*)ap_info[i].ssid);
        cJSON_AddNumberToObject(ap_json, "rssi", ap_info[i].rssi);
        cJSON_AddNumberToObject(ap_json, "authmode", ap_info[i].authmode);
        cJSON_AddStringToObject(ap_json, "authType", get_auth_type_string(ap_info[i].authmode));
        cJSON_AddItemToArray(json_array, ap_json);

        ESP_LOGI(TAG, "WiFi found: SSID=%s, RSSI=%d, Auth=%s",
                 ap_info[i].ssid, ap_info[i].rssi, get_auth_type_string(ap_info[i].authmode));
    }

    char *json_string = cJSON_Print(json_array);
    send_json_response(req, 200, "Success", json_string);

    free(json_string);
    cJSON_Delete(json_array);

    return ESP_OK;
}

// 系统重置处理器
esp_err_t system_reset_handler(httpd_req_t *req)
{
    send_json_response(req, 200, "System will reset in 3 seconds", NULL);

    // 延时3秒后重启
    vTaskDelay(3000 / portTICK_PERIOD_MS);
    esp_restart();

    return ESP_OK;
}

// favicon处理器
esp_err_t favicon_handler(httpd_req_t *req)
{
    // 返回一个简单的404响应，避免错误日志
    httpd_resp_set_status(req, "404 Not Found");
    httpd_resp_send(req, NULL, 0);
    return ESP_OK;
}

// 启动HTTP服务器
esp_err_t http_server_start(void)
{
    if (g_softap_server != NULL) {
        return ESP_OK;
    }

    httpd_config_t config = HTTPD_DEFAULT_CONFIG();
    config.server_port = HTTP_SERVER_PORT;
    config.max_open_sockets = 7;
    config.max_uri_handlers = 20;       // 增加URI处理器数量
    config.lru_purge_enable = true;
    config.max_resp_headers = 16;       // 增加响应头数量
    config.stack_size = 16384;          // 大幅增加栈大小
    config.recv_wait_timeout = 20;      // 增加接收超时
    config.send_wait_timeout = 20;      // 增加发送超时
    config.backlog_conn = 5;            // 增加连接队列
    config.core_id = tskNO_AFFINITY;    // 允许在任何核心运行

    ESP_LOGI(TAG, "Starting HTTP server on port: '%d'", config.server_port);

    if (httpd_start(&g_softap_server, &config) == ESP_OK) {
        int registered_count = 0;

        // 注册基本URI处理器
        httpd_uri_t handlers[] = {
            {API_ROOT, HTTP_GET, simple_config_page_handler, NULL},
            {API_DEVICE_INFO, HTTP_GET, device_info_handler, NULL},
            {API_WIFI_CONFIG, HTTP_POST, wifi_config_handler, NULL},
            {API_WIFI_SCAN, HTTP_GET, wifi_scan_handler, NULL},
            {API_WIFI_STATUS, HTTP_GET, wifi_status_handler, NULL},
            {"/config.js", HTTP_GET, config_js_handler, NULL},
            {"/favicon.ico", HTTP_GET, favicon_handler, NULL}
        };

        int handler_count = sizeof(handlers) / sizeof(handlers[0]);

        for (int i = 0; i < handler_count; i++) {
            if (httpd_register_uri_handler(g_softap_server, &handlers[i]) == ESP_OK) {
                registered_count++;
            } else {
                ESP_LOGW(TAG, "Failed to register handler for %s", handlers[i].uri);
            }
        }

        ESP_LOGI(TAG, "HTTP server started with %d/%d handlers", registered_count, handler_count);
        return ESP_OK;
    }

    ESP_LOGE(TAG, "Failed to start HTTP server");
    return ESP_FAIL;
}

// 停止HTTP服务器
esp_err_t http_server_stop(void)
{
    if (g_softap_server) {
        httpd_stop(g_softap_server);
        g_softap_server = NULL;
        ESP_LOGI(TAG, "HTTP server stopped");
    }
    return ESP_OK;
}
